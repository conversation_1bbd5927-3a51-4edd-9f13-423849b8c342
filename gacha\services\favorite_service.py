from typing import Dict, List, Optional

from database.postgresql.async_manager import get_pool
from gacha.exceptions import (
    BusinessError,
    CardNotFoundError,
    DatabaseOperationError,
    FavoriteCardError,
)
from gacha.models.models import UserCard
from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import user_collection_repository
from gacha.services import user_service
from utils.logger import logger


async def toggle_favorite_card(user_id: int, card_id: int, operator_id: int) -> bool:
    """
    切換卡片的最愛狀態，並更新 custom_sort_index。

    Args:
        user_id: 卡片擁有者的ID
        card_id: 卡片ID
        operator_id: 執行操作的用戶ID

    Returns:
        bool: 新的最愛狀態

    Raises:
        BusinessError: 如果操作者不是卡片擁有者
        CardNotFoundError: 找不到卡片或用戶不擁有此卡片
        FavoriteCardError: 更新最愛狀態失敗
    """
    if user_id != operator_id:
        raise BusinessError("只有卡片擁有者才能修改最愛狀態。")
    master_card = await master_card_repository.get_card(card_id)
    if not master_card:
        raise CardNotFoundError(f"找不到此卡片 (ID: {card_id})", card_id=card_id)

    master_card_id_for_event = master_card.card_id

    user_card = await user_collection_repository.get_user_card(user_id, card_id)

    current_is_favorite = user_card.is_favorite
    new_is_favorite = not current_is_favorite
    sort_indexes_map = None

    pool = get_pool()
    if pool is None:
        raise DatabaseOperationError("資料庫連線池未初始化")
    async with pool.acquire() as connection:
        async with connection.transaction():
            if new_is_favorite:
                max_index = await user_collection_repository.get_max_custom_sort_index(
                    user_id, connection=connection
                )
                base_index = (max_index if max_index is not None else -1000) + 1000
                sort_indexes_map = {card_id: base_index}

            try:
                result_repo = (
                    await user_collection_repository.batch_set_favorite_status_raw(
                        user_id,
                        [card_id],
                        new_is_favorite,
                        sort_indexes_map if new_is_favorite else None,
                        connection=connection,
                    )
                )
                updated_count = result_repo.get("updated_count", 0)
                if updated_count == 0:
                    logger.warning(
                        "toggle_favorite_card: No rows updated for user %s, card %s",
                        user_id,
                        card_id,
                    )
            except Exception as e:
                logger.error(
                    "toggle_favorite_card: batch_set_favorite_status_raw failed for user %s, card %s: %s",
                    user_id,
                    card_id,
                    str(e),
                )
                raise FavoriteCardError(
                    f"更新卡片最愛狀態失敗: {str(e)}", card_id=card_id
                ) from e

            # 【新增】在同一事務中更新市場統計
            from gacha.services.direct_market_stats_updater import (
                update_market_stats_in_transaction,
            )

            favorite_changes = [
                (master_card_id_for_event, 1 if new_is_favorite else -1)
            ]
            await update_market_stats_in_transaction(
                conn=connection,
                drawn_card_ids=[],
                owner_changes=[],
                favorite_changes=favorite_changes,
            )

    # 統計更新已在主事務中完成
    logger.debug(
        "[FAVORITE_SERVICE] 收藏狀態切換完成 - 用戶: %s, 卡片: %s, 新狀態: %s",
        user_id,
        card_id,
        new_is_favorite,
    )

    return new_is_favorite


async def set_favorite_status(
    user_id: int, card_id: int, status: bool, operator_id: int
) -> bool:
    """
    直接設置卡片的最愛狀態

    Args:
        user_id: 用戶ID
        card_id: 卡片ID
        status: 目標最愛狀態
        operator_id: 執行操作的用戶ID

    Returns:
        bool: 設置後的最愛狀態

    Raises:
        CardNotFoundError: 找不到卡片或用戶不擁有此卡片
        FavoriteCardError: 更新最愛狀態失敗
    """
    user_card = await user_collection_repository.get_user_card(user_id, card_id)

    if user_card.is_favorite == status:
        return status

    return await toggle_favorite_card(user_id, card_id, operator_id)


async def get_favorite_cards(user_id: int) -> List[UserCard]:
    """獲取用戶所有標記為最愛的卡片"""
    # 根據規範，移除 try-except，讓錯誤自然冒泡
    # 如果 user_service.get_user 找不到用戶，它會拋出 UserNotFoundError
    await user_service.get_user(user_id)
    # 如果 user_collection_repository.get_favorite_user_cards 失敗，它會拋出資料庫相關異常
    return await user_collection_repository.get_favorite_user_cards(user_id)


async def batch_favorite_cards(user_id: int, card_ids: List[int]) -> int:
    """
    批量將卡片加入最愛，並計算排序索引

    Args:
        user_id: 用戶ID
        card_ids: 要加入最愛的卡片ID列表

    Returns:
        int: 成功加入最愛的卡片數量

    Raises:
        ValueError: 未提供卡片ID
        CardNotFoundError: 用戶未擁有任何指定的卡片
        DatabaseOperationError: 數據庫操作失敗
    """
    if not card_ids:
        raise BusinessError("沒有指定要加入最愛的卡片")

    updated_count_from_repo = 0
    actually_favorited_ids = []

    pool = get_pool()
    if pool is None:
        raise DatabaseOperationError("資料庫連線池未初始化")
    async with pool.acquire() as connection:
        async with connection.transaction():
            all_owned_ids = (
                await user_collection_repository.get_owned_card_ids_in_batch(
                    user_id, card_ids, connection=connection
                )
            )

            if not all_owned_ids:
                raise CardNotFoundError("用戶未擁有任何指定的卡片")

            already_fav_ids = (
                await user_collection_repository.get_favorite_card_ids_in_batch(
                    user_id, all_owned_ids, connection=connection
                )
            )

            valid_card_ids_to_favorite = [
                cid for cid in all_owned_ids if cid not in already_fav_ids
            ]

            if not valid_card_ids_to_favorite:
                return 0

            max_index = await user_collection_repository.get_max_custom_sort_index(
                user_id, connection=connection
            )
            base_index = (max_index if max_index is not None else -1000) + 1000

            # 直接從資料庫獲取已排序的卡片列表
            sorted_card_details = (
                await user_collection_repository.get_cards_for_batch_favorite_sorted(
                    user_id, valid_card_ids_to_favorite, connection=connection
                )
            )

            if not sorted_card_details:
                raise DatabaseOperationError("無法獲取卡片詳細信息")

            # 現在不需要在Python中排序了

            sort_indexes_map: Dict[int, int] = {
                card_detail["card_id"]: base_index + i * 1000
                for i, card_detail in enumerate(sorted_card_details)
            }

            # 根據規範，不再捕捉通用 Exception，讓資料庫錯誤自然冒泡
            result_repo = (
                await user_collection_repository.batch_set_favorite_status_raw(
                    user_id,
                    valid_card_ids_to_favorite,
                    True,
                    sort_indexes_map,
                    connection=connection,
                )
            )
            updated_count_from_repo = result_repo.get("updated_count", 0)

            if updated_count_from_repo > 0:
                actually_favorited_ids = valid_card_ids_to_favorite[
                    :updated_count_from_repo
                ]

                # 【新增】在同一事務中更新市場統計
                from gacha.services.direct_market_stats_updater import (
                    update_market_stats_in_transaction,
                )

                favorite_changes = [(card_id, 1) for card_id in actually_favorited_ids]
                await update_market_stats_in_transaction(
                    conn=connection,
                    drawn_card_ids=[],
                    owner_changes=[],
                    favorite_changes=favorite_changes,
                )

    # 統計更新已在主事務中完成，無需額外處理
    logger.debug(
        "[FAVORITE_SERVICE] 批量收藏完成 - 用戶: %s, 成功數量: %s",
        user_id,
        updated_count_from_repo,
    )

    return updated_count_from_repo


async def batch_unfavorite_cards(user_id: int, card_ids: List[int]) -> int:
    """
    批量將卡片移出最愛，並觸發市場統計更新事件

    Args:
        user_id: 用戶ID
        card_ids: 要移出最愛的卡片ID列表

    Returns:
        int: 成功移出最愛的卡片數量

    Raises:
        ValueError: 未提供卡片ID
        DatabaseOperationError: 數據庫操作失敗
    """
    if not card_ids:
        raise BusinessError("沒有指定要移出最愛的卡片")

    updated_count_from_repo = 0
    unfavorited_card_ids_for_event = []

    pool = get_pool()
    if pool is None:
        raise DatabaseOperationError("資料庫連線池未初始化")
    async with pool.acquire() as connection:
        async with connection.transaction():
            currently_favorited_ids_in_batch = (
                await user_collection_repository.get_favorite_card_ids_in_batch(
                    user_id, card_ids, connection=connection
                )
            )

            if not currently_favorited_ids_in_batch:
                return 0

            ids_to_actually_unfavorite = [
                cid for cid in card_ids if cid in currently_favorited_ids_in_batch
            ]

            if not ids_to_actually_unfavorite:
                return 0

            # 根據規範，不再捕捉通用 Exception，讓資料庫錯誤自然冒泡
            result_repo = (
                await user_collection_repository.batch_set_favorite_status_raw(
                    user_id,
                    ids_to_actually_unfavorite,
                    False,
                    None,
                    connection=connection,
                )
            )
            updated_count_from_repo = result_repo.get("updated_count", 0)
            unfavorited_card_ids_for_event = ids_to_actually_unfavorite[
                :updated_count_from_repo
            ]

            # 【新增】在同一事務中更新市場統計
            if updated_count_from_repo > 0:
                from gacha.services.direct_market_stats_updater import (
                    update_market_stats_in_transaction,
                )

                favorite_changes = [
                    (card_id, -1) for card_id in unfavorited_card_ids_for_event
                ]
                await update_market_stats_in_transaction(
                    conn=connection,
                    drawn_card_ids=[],
                    owner_changes=[],
                    favorite_changes=favorite_changes,
                )

    # 統計更新已在主事務中完成，無需額外處理
    logger.debug(
        "[FAVORITE_SERVICE] 批量取消收藏完成 - 用戶: %s, 成功數量: %s",
        user_id,
        updated_count_from_repo,
    )

    return updated_count_from_repo


def get_filtered_cards_description(
    pool_type: Optional[str] = None,
    rarity_filter: Optional[List[int]] = None,
    series_filter: Optional[str] = None,
) -> str:
    """獲取經過篩選的卡片描述文本 (處理數字稀有度)"""
    filters = []
    if pool_type:
        filters.append(f"卡池: {pool_type}")
    if rarity_filter:
        from config.app_config import get_rarity_display_codes

        display_rarities = [
            get_rarity_display_codes().get(r, str(r)) for r in rarity_filter
        ]
        filters.append(f"稀有度: {', '.join(display_rarities)}")
    if series_filter:
        filters.append(f"系列: {series_filter}")
    return " | ".join(filters) if filters else "全部卡片"
