from __future__ import annotations

import random
from typing import Any, Dict, List, Optional

from gacha.repositories._base_repo import fetch_all
from utils.logger import logger

# --- 模組級別的全局主快取 ---

# _master_card_cache:
# - 類型: Optional[List[Dict[str, Any]]]
# - 作用: 儲存 gacha_master_cards 表中的所有卡片資料。
# - 特性: 在機器人生命週期內只會被填充一次，之後永久有效。
_master_card_cache: Optional[List[Dict[str, Any]]] = None


async def get_master_card_cache() -> List[Dict[str, Any]]:
    """
    獲取卡片主資料快取。

    如果快取尚未初始化，此函數將會觸發一次從資料庫的完全載入。
    之後的調用將直接從記憶體返回快取的資料。

    Returns:
        一個包含所有主卡片資料的字典列表。
    """
    global _master_card_cache
    if _master_card_cache is None:
        logger.info("[CACHE] 卡片主資料快取尚未初始化，正在從資料庫載入...")
        _master_card_cache = await _populate_master_card_cache()
    else:
        logger.debug("[CACHE] 命中卡片主資料快取。")

    return _master_card_cache


async def _populate_master_card_cache() -> List[Dict[str, Any]]:
    """
    從資料庫中獲取所有主卡片資料來填充快取。
    此函數在整個應用程式生命週期中理論上只應被執行一次。
    """
    # 效能更優的修復方案：從資料庫高效讀取，然後在應用程式內進行隨機洗牌。
    query = "SELECT card_id, pool_type, rarity FROM gacha_master_cards"
    try:
        all_cards_records = await fetch_all(query)
        # 將 asyncpg.Record 轉換為標準字典，以減少潛在的記憶體問題和依賴
        all_cards_dicts = [dict(record) for record in all_cards_records]

        # 關鍵步驟：在應用程式層面對列表進行隨機洗牌，避免了昂貴的資料庫排序。
        random.shuffle(all_cards_dicts)

        logger.info(
            f"[CACHE] 卡片主資料快取載入成功，共載入 {len(all_cards_dicts)} 張卡片並已隨機化。"
        )
        return all_cards_dicts
    except Exception as e:
        logger.error("❌ [CACHE] 填充卡片主資料快取失敗: %s", e, exc_info=True)
        # 在失敗時返回一個空列表，避免 None 導致的連鎖錯誤，
        # 但下一次 get_master_card_cache 調用時會因為 _master_card_cache 仍為 None 而重試。
        return []


def clear_card_pool_cache() -> None:
    """
    手動清除卡片主資料快取。
    主要用於測試或在極少數需要熱重載卡片資料的場景。
    """
    global _master_card_cache
    _master_card_cache = None
    logger.info("[CACHE] 卡片主資料快取已被手動清除。下次請求時將會從資料庫重新載入。")
