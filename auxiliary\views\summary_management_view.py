# summary_management_view_v2.py

import functools
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Tuple, Union

import discord
from discord.ext import commands

from auxiliary.services import story_logic
from auxiliary.services.story.constants import (
    CHUNK_SIZE_FLOORS,
    MAX_BUTTON_ROWS,
    PROTECTED_RECENT_FLOORS,
    SUMMARY_PAGE_SIZE,
)
from utils.base_view import BaseView
from utils.text_utils import split_text_nicely

if TYPE_CHECKING:
    from auxiliary.cogs.story_cog import StoryCog


class SummaryManagementView(BaseView):
    """
    獨立的總結管理視圖類
    
    這個類專門負責處理故事的總結管理功能，包括：
    - 顯示可總結的區塊
    - 生成/重新生成總結
    - 查看總結內容
    - 分頁顯示總結內容
    
    設計原則：
    - 不再繼承 BasePaginationView（不需要分頁功能）
    - 使用回調函數機制而非直接引用避免循環引用
    - 清晰的職責分離
    """

    def __init__(
        self,
        bot: Union[commands.Bot, commands.AutoShardedBot],
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
        story_id: str,
        story_meta: Dict[str, Any],
        total_turn_count: int,
        back_callback: Optional[Callable[[discord.Interaction], Any]] = None,
    ):
        super().__init__(bot=bot, user_id=user.id, timeout=None)
        
        self.story_cog = story_cog
        self.story_id = story_id
        self.story_meta = story_meta
        self.total_turn_count = total_turn_count
        self.back_callback = back_callback  # 回調函數而非直接引用
        
        # 緩存管理
        self._cached_chunk_summaries = None
        self._cache_timestamp = None
        
        # 查看模式狀態
        self.current_viewing_chunk: Optional[Tuple[int, int, int, List[str]]] = None
        self.current_summary_page = 1
        self.total_summary_pages = 1

    def _calculate_chunks(self) -> List[Dict[str, Any]]:
        """計算所有可總結的區塊信息。"""
        total_floors = self.total_turn_count // 2
        chunks = []

        for chunk_start in range(1, total_floors, CHUNK_SIZE_FLOORS):
            chunk_end = min(chunk_start + CHUNK_SIZE_FLOORS - 1, total_floors)
            turn_start = (chunk_start - 1) * 2 + 1
            turn_end = chunk_end * 2

            # 只有不是最新的區塊才可以總結
            if chunk_end < total_floors - PROTECTED_RECENT_FLOORS:
                chunk_num = (chunk_start - 1) // CHUNK_SIZE_FLOORS + 1
                chunks.append(
                    {
                        "chunk_num": chunk_num,
                        "floors": f"{chunk_start}-{chunk_end}",
                        "turns": f"{turn_start}-{turn_end}",
                        "start_turn": turn_start,
                        "end_turn": turn_end,
                    }
                )

        return chunks

    async def _get_chunk_summaries_with_cache(self) -> Dict[tuple, Dict[str, Any]]:
        """獲取區塊總結，使用緩存優化性能。"""
        import time

        current_time = time.time()

        # 如果緩存存在且在5分鐘內，使用緩存
        if (
            self._cached_chunk_summaries is not None
            and self._cache_timestamp is not None
            and current_time - self._cache_timestamp < 300
        ):
            return self._cached_chunk_summaries

        # 重新獲取數據
        try:
            chunk_summaries = (
                await story_logic.story_repository.get_chunk_summaries_for_story(
                    self.story_id
                )
            )
            existing_summaries = {
                (s["start_turn_number"], s["end_turn_number"]): s for s in chunk_summaries
            }

            # 更新緩存
            self._cached_chunk_summaries = existing_summaries
            self._cache_timestamp = current_time

            return existing_summaries
        except Exception as e:
            # 如果獲取失敗，返回空字典而不是崩潰
            return {}

    def _invalidate_summary_cache(self):
        """使緩存失效（當總結被修改時調用）。"""
        self._cached_chunk_summaries = None
        self._cache_timestamp = None

    async def start(self) -> discord.Embed:
        """
        啟動總結管理視圖，返回初始嵌入
        """
        await self._add_management_buttons()
        return await self._create_management_embed()

    async def _create_management_embed(self) -> discord.Embed:
        """創建總結管理模式的嵌入。"""
        embed = discord.Embed(
            title="🗂️ 長期記憶總結管理",
            description="管理故事的區塊總結，以優化記憶體使用和上下文長度。",
            color=discord.Color.blue(),
        )

        # 使用統一方法獲取區塊和總結信息
        chunks = self._calculate_chunks()
        existing_summaries = await self._get_chunk_summaries_with_cache()

        # 為區塊添加總結狀態
        chunk_info = []
        for chunk in chunks:
            chunk_key = (chunk["start_turn"], chunk["end_turn"])
            chunk_info.append(
                {
                    **chunk,
                    "has_summary": chunk_key in existing_summaries,
                }
            )

        if not chunk_info:
            embed.add_field(
                name="📄 區塊狀態",
                value="當前故事長度還不足以進行區塊總結。\n建議在超過40樓層後再使用此功能。",
                inline=False,
            )
        else:
            status_text = ""
            for chunk in chunk_info:
                status_text += f"**區塊 {chunk['chunk_num']}**: 樓層 {chunk['floors']} (回合 {chunk['turns']})\n"
                if chunk["has_summary"]:
                    status_text += "狀態: ✅ 已總結\n\n"
                else:
                    status_text += "狀態: 🔄 待總結\n\n"

            embed.add_field(name="📄 區塊狀態", value=status_text.strip(), inline=False)

        embed.add_field(
            name="💡 說明",
            value="• 每40樓層可合併為一個「大總結」\n• 總結後可顯著減少上下文長度\n• 最近10樓層會保持完整記憶",
            inline=False,
        )

        return embed

    async def _add_management_buttons(self):
        """添加總結管理模式的按鈕。"""
        # 清除現有按鈕
        self.clear_items()
        
        # 使用統一方法獲取區塊和總結信息
        chunks = self._calculate_chunks()
        existing_summaries = await self._get_chunk_summaries_with_cache()

        current_row = 0

        # 為每個可總結的區塊生成按鈕
        for chunk in chunks:
            chunk_key = (chunk["start_turn"], chunk["end_turn"])
            chunk_num = chunk["chunk_num"]
            turn_start = chunk["start_turn"]
            turn_end = chunk["end_turn"]

            if chunk_key in existing_summaries:
                # 已有總結：顯示查看和重新生成按鈕
                view_button = discord.ui.Button(
                    label=f"👁️ 查看區塊{chunk_num}",
                    style=discord.ButtonStyle.success,
                    custom_id=f"view_chunk_{chunk_num}",
                    row=current_row,
                )
                # 使用偏函數替代 lambda 避免閉包陷阱
                view_button.callback = functools.partial(
                    self.view_chunk_callback,
                    chunk_num=chunk_num,
                    start_turn=turn_start,
                    end_turn=turn_end
                )
                self.add_item(view_button)

                regenerate_button = discord.ui.Button(
                    label=f"🔄 重新生成{chunk_num}",
                    style=discord.ButtonStyle.danger,
                    custom_id=f"regenerate_chunk_{chunk_num}",
                    row=current_row,
                )
                # 使用偏函數替代 lambda 避免閉包陷阱
                regenerate_button.callback = functools.partial(
                    self._generate_chunk_summary,
                    chunk_num=chunk_num,
                    start_turn=turn_start,
                    end_turn=turn_end,
                    is_regenerate=True
                )
                self.add_item(regenerate_button)
            else:
                # 未有總結：顯示生成按鈕
                generate_button = discord.ui.Button(
                    label=f"✍️ 生成區塊{chunk_num}總結",
                    style=discord.ButtonStyle.primary,
                    custom_id=f"generate_chunk_{chunk_num}",
                    row=current_row,
                )
                # 使用偏函數替代 lambda 避免閉包陷阱
                generate_button.callback = functools.partial(
                    self._generate_chunk_summary,
                    chunk_num=chunk_num,
                    start_turn=turn_start,
                    end_turn=turn_end,
                    is_regenerate=False
                )
                self.add_item(generate_button)

            current_row += 1
            if current_row >= MAX_BUTTON_ROWS:  # 避免超出Discord限制
                break

        # 返回故事按鈕
        back_button = discord.ui.Button(
            label="⬅️ 返回故事",
            style=discord.ButtonStyle.secondary,
            custom_id="back_to_story",
            row=4,
        )
        back_button.callback = self.back_to_story_callback
        self.add_item(back_button)

    async def _show_loading_embed(
        self, interaction: discord.Interaction, title: str, description: str
    ):
        """顯示統一的載入界面。"""
        await interaction.response.edit_message(
            embed=discord.Embed(
                title=f"<a:Loading:1392930453219967149> {title}",
                description=description,
                color=discord.Color.orange(),
            ),
            view=self,
        )

    async def _show_error_embed(
        self, interaction: discord.Interaction, title: str, description: str
    ):
        """顯示統一的錯誤界面。"""
        await interaction.edit_original_response(
            embed=discord.Embed(
                title=f"❌ {title}",
                description=description,
                color=discord.Color.red(),
            ),
            view=self,
        )

    async def _reload_management_interface(
        self, interaction: discord.Interaction, success_message: Optional[str] = None
    ):
        """統一的管理界面重載方法。"""
        self._invalidate_summary_cache()  # 使緩存失效
        await self._add_management_buttons()
        embed = await self._create_management_embed()

        if success_message:
            embed.color = discord.Color.green()
            embed.add_field(name="✅ 操作完成", value=success_message, inline=False)

        await interaction.edit_original_response(embed=embed, view=self)

    async def _generate_chunk_summary(
        self,
        interaction: discord.Interaction,
        *,
        chunk_num: int,
        start_turn: int,
        end_turn: int,
        is_regenerate: bool = False,
    ):
        """統一的區塊總結生成方法。"""
        action = "重新生成" if is_regenerate else "生成"

        # 顯示載入界面
        await self._show_loading_embed(
            interaction,
            f"正在{action}總結...",
            f"AI 正在{'重新' if is_regenerate else ''}分析區塊{chunk_num}的內容並生成總結，請稍候...",
        )

        try:
            # 如果是重新生成，先刪除舊總結
            if is_regenerate:
                await story_logic.story_repository.delete_chunk_summary_by_range(
                    self.story_id, start_turn, end_turn
                )

            # 生成總結
            success = await story_logic.generate_chunk_summary(
                user_id=self.user_id,
                story_id=self.story_id,
                start_turn_number=start_turn,
                end_turn_number=end_turn,
            )

            if success:
                success_message = f"已成功{action}區塊{chunk_num}的總結！"
                await self._reload_management_interface(interaction, success_message)
            else:
                await self._show_error_embed(
                    interaction,
                    f"{action}失敗",
                    f"{action}總結時發生錯誤，請稍後再試。",
                )

        except story_logic.AIConnectionError:
            await self._show_error_embed(
                interaction, "AI 連接錯誤", f"無法連接到 AI 服務，請稍後再試。"
            )
        except Exception as e:
            await self._show_error_embed(
                interaction, "發生錯誤", f"{action}總結時發生意外錯誤: {str(e)}"
            )

    async def view_chunk_callback(
        self,
        interaction: discord.Interaction,
        *,
        chunk_num: int,
        start_turn: int,
        end_turn: int,
    ):
        """查看區塊總結的回調函數（支援切頁）。"""
        try:
            # 獲取總結內容
            chunk_summaries = (
                await story_logic.story_repository.get_chunk_summaries_for_story(
                    self.story_id
                )
            )
            target_summary = None

            for summary in chunk_summaries:
                if (
                    summary["start_turn_number"] == start_turn
                    and summary["end_turn_number"] == end_turn
                ):
                    target_summary = summary
                    break

            if not target_summary:
                await interaction.response.edit_message(
                    embed=discord.Embed(
                        title="❌ 找不到總結",
                        description="無法找到該區塊的總結內容。",
                        color=discord.Color.red(),
                    ),
                    view=self,
                )
                return

            # 設置查看模式狀態
            content = target_summary["content"]

            # 將內容分頁顯示（每頁最多SUMMARY_PAGE_SIZE字符）
            content_chunks = split_text_nicely(content, SUMMARY_PAGE_SIZE)
            self.current_viewing_chunk = (
                chunk_num,
                start_turn,
                end_turn,
                content_chunks,
            )
            self.current_summary_page = 1
            self.total_summary_pages = len(content_chunks)

            # 創建查看總結的嵌入
            embed = self._create_chunk_view_embed()

            # 清除現有按鈕並添加查看模式按鈕
            self.clear_items()
            self._add_chunk_view_buttons()

            await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            await interaction.response.edit_message(
                embed=discord.Embed(
                    title="❌ 發生錯誤",
                    description=f"查看總結時發生錯誤: {str(e)}",
                    color=discord.Color.red(),
                ),
                view=self,
            )

    def _create_chunk_view_embed(self) -> discord.Embed:
        """創建查看區塊總結的嵌入（支援分頁）。"""
        if not self.current_viewing_chunk:
            return discord.Embed(
                title="錯誤", description="無查看內容", color=discord.Color.red()
            )

        chunk_num, start_turn, end_turn, content_chunks = self.current_viewing_chunk
        current_content = content_chunks[self.current_summary_page - 1]

        embed = discord.Embed(
            title=f"📄 區塊{chunk_num}總結 (回合 {start_turn}-{end_turn})",
            description=current_content,
            color=discord.Color.blue(),
        )

        if self.total_summary_pages > 1:
            embed.set_footer(
                text=f"頁面 {self.current_summary_page}/{self.total_summary_pages}"
            )

        return embed

    def _add_chunk_view_buttons(self):
        """添加查看區塊總結時的按鈕。"""
        # 分頁按鈕（如果有多頁）
        if self.total_summary_pages > 1:
            if self.current_summary_page > 1:
                prev_button = discord.ui.Button(
                    label="⬅️ 上一頁",
                    style=discord.ButtonStyle.secondary,
                    custom_id="prev_summary_page",
                    row=0,
                )
                prev_button.callback = self.prev_summary_page_callback
                self.add_item(prev_button)

            if self.current_summary_page < self.total_summary_pages:
                next_button = discord.ui.Button(
                    label="➡️ 下一頁",
                    style=discord.ButtonStyle.secondary,
                    custom_id="next_summary_page",
                    row=0,
                )
                next_button.callback = self.next_summary_page_callback
                self.add_item(next_button)

        # 返回管理界面按鈕
        back_to_management_button = discord.ui.Button(
            label="⬅️ 返回管理",
            style=discord.ButtonStyle.secondary,
            custom_id="back_to_management",
            row=4,
        )
        back_to_management_button.callback = self.back_to_management_callback
        self.add_item(back_to_management_button)

    async def prev_summary_page_callback(self, interaction: discord.Interaction):
        """上一頁總結內容的回調函數。"""
        if self.current_summary_page > 1:
            self.current_summary_page -= 1
            embed = self._create_chunk_view_embed()
            self.clear_items()
            self._add_chunk_view_buttons()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    async def next_summary_page_callback(self, interaction: discord.Interaction):
        """下一頁總結內容的回調函數。"""
        if self.current_summary_page < self.total_summary_pages:
            self.current_summary_page += 1
            embed = self._create_chunk_view_embed()
            self.clear_items()
            self._add_chunk_view_buttons()
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    async def back_to_management_callback(self, interaction: discord.Interaction):
        """從查看模式返回總結管理界面的回調函數。"""
        self.current_viewing_chunk = None
        self.current_summary_page = 1
        self.total_summary_pages = 1

        # 重新載入管理界面
        await self._add_management_buttons()
        embed = await self._create_management_embed()

        await interaction.response.edit_message(embed=embed, view=self)

    async def back_to_story_callback(self, interaction: discord.Interaction):
        """返回故事模式的回調函數。"""
        if self.back_callback:
            # 使用回調函數而非直接引用，避免循環引用
            await self.back_callback(interaction)
        else:
            # 如果沒有回調函數，顯示錯誤信息
            await interaction.response.send_message(
                "無法返回故事界面，請重新啟動。", ephemeral=True
            )