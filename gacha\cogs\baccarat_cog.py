"""
Gacha系統 百家樂遊戲 COG
處理百家樂遊戲的Discord命令和交互
所有邏輯都在單一COG內部，便於管理和熱重載
"""

import asyncio
import math
import random
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Literal, Optional

import discord
from discord import app_commands
from discord.ext import commands, tasks
from discord.ui import Button, TextInput, View

import gacha.services.economy_service as economy_service
import gacha.services.game_stats_service as game_stats_service
from gacha.exceptions import (
    GameError,
    InsufficientBalanceError,
    InvalidBetAmountError,
    InvalidOperationError,
)
from utils.base_modal import BaseModal
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# --- 常量定義 ---

DEAL_DELAY = 1.5  # 開牌動畫延遲秒數

CARD_EMOJIS = {
    ("diamonds", "A"): "<:D1:1367810486250508299>",
    ("diamonds", "2"): "<:D2:1367810491153383496>",
    ("diamonds", "3"): "<:D3:1367810497176404049>",
    ("diamonds", "4"): "<:D4:1367810508203360256>",
    ("diamonds", "5"): "<:D5:1367810514591154216>",
    ("diamonds", "6"): "<:D6:1367810519951478815>",
    ("diamonds", "7"): "<:D7:1367810525056208896>",
    ("diamonds", "8"): "<:D8:1367810530500149359>",
    ("diamonds", "9"): "<:D9:1367810536758317097>",
    ("diamonds", "10"): "<:D10:1367810543020281926>",
    ("diamonds", "J"): "<:D11:1367810548686655639>",
    ("diamonds", "Q"): "<:D12:1367810555687079986>",
    ("diamonds", "K"): "<:D13:1367810561579946075>",
    ("spades", "A"): "<:S1:1367810413135269922>",
    ("spades", "2"): "<:S2:1367810419330252921>",
    ("spades", "3"): "<:S3:1367810424145313916>",
    ("spades", "4"): "<:S4:1367810429509697639>",
    ("spades", "5"): "<:S5:1367810434492797008>",
    ("spades", "6"): "<:S6:1367810439353864252>",
    ("spades", "7"): "<:S7:1367810444596875284>",
    ("spades", "8"): "<:S8:1367810449650880542>",
    ("spades", "9"): "<:S9:1367810454902276192>",
    ("spades", "10"): "<:S10:1367810459662684259>",
    ("spades", "J"): "<:S11:1367810466344079450>",
    ("spades", "Q"): "<:S12:1367810475378872400>",
    ("spades", "K"): "<:S13:1367810480999235675>",
    ("clubs", "A"): "<:C1:1367810267848773673>",
    ("clubs", "2"): "<:C2:1367810277525033061>",
    ("clubs", "3"): "<:C3:1367810294914748426>",
    ("clubs", "4"): "<:C4:1367810299985661993>",
    ("clubs", "5"): "<:C5:1367810304695730196>",
    ("clubs", "6"): "<:C6:1367810310202720366>",
    ("clubs", "7"): "<:C7:1367810314564931645>",
    ("clubs", "8"): "<:C8:1367810319610675272>",
    ("clubs", "9"): "<:C9:1367810324706623598>",
    ("clubs", "10"): "<:C10:1367810329895243868>",
    ("clubs", "J"): "<:C11:1367810333892280320>",
    ("clubs", "Q"): "<:C12:1367810337805701230>",
    ("clubs", "K"): "<:C13:1367810342515769385>",
    ("hearts", "A"): "<:H1:1367810346802348143>",
    ("hearts", "2"): "<:H2:1367810351818870896>",
    ("hearts", "3"): "<:H3:1367810356247920670>",
    ("hearts", "4"): "<:H4:1367810361448726649>",
    ("hearts", "5"): "<:H5:1367810366444142634>",
    ("hearts", "6"): "<:H6:1367810372156915782>",
    ("hearts", "7"): "<:H7:1367810377202532392>",
    ("hearts", "8"): "<:H8:1367810382135033896>",
    ("hearts", "9"): "<:H9:1367810387818450975>",
    ("hearts", "10"): "<:H10:1367810391257907200>",
    ("hearts", "J"): "<:H11:1367810396844462104>",
    ("hearts", "Q"): "<:H12:1367810402255110188>",
    ("hearts", "K"): "<:H13:1367810407791722516>",
    ("unknown", "?"): "❓",
}
BACCARAT_IMAGE_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1389314738042704017/baccarat_1.png?ex=68642bad&is=6862da2d&hm=cf438374b1ec605df1a22989cec62fc039fc123c5df3b3b311238ece3af19d03&"

# --- 遊戲核心類 ---


@dataclass
class PlayingCard:
    suit: str
    value: str

    @property
    def baccarat_value(self) -> int:
        if self.value in ["10", "J", "Q", "K"]:
            return 0
        if self.value == "A":
            return 1
        return int(self.value)

    def __str__(self) -> str:
        return CARD_EMOJIS.get((self.suit, self.value), "?")

    @property
    def short_name(self) -> str:
        return f"{self.suit[0].upper()}{self.value}"


class Deck:
    def __init__(self, num_decks: int = 8):
        s, v = (
            ["hearts", "spades", "diamonds", "clubs"],
            ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"],
        )
        self.cards = [
            PlayingCard(suit, val) for _ in range(num_decks) for suit in s for val in v
        ]
        self.shuffle()

    def shuffle(self):
        random.shuffle(self.cards)

    def deal(self) -> Optional[PlayingCard]:
        return self.cards.pop() if self.cards else None


class Hand:
    def __init__(self):
        self.cards: List[PlayingCard] = []

    def add_card(self, card: PlayingCard):
        self.cards.append(card)

    @property
    def value(self) -> int:
        return sum(c.baccarat_value for c in self.cards) % 10

    def __str__(self) -> str:
        return " ".join(str(c) for c in self.cards)

    def to_short_string(self) -> str:
        return " ".join(c.short_name for c in self.cards)


@dataclass
class Player:
    user_id: int
    user_name: str
    bet_on: Literal["閒家", "莊家", "和局"]
    amount: int


@dataclass
class BaccaratResult:
    """封裝單一玩家的遊戲結果，用於結算和統計"""

    player: Player
    game_winner: Literal["閒家", "莊家", "和局"]
    player_hand: Hand
    banker_hand: Hand

    payout: int = field(init=False)
    status: str = field(init=False)
    profit: int = field(init=False)

    def __post_init__(self):
        """在初始化後計算賠付和狀態"""
        payout, status = 0, "輸"
        if self.player.bet_on == self.game_winner:
            status = "贏"
            if self.player.bet_on == "閒家":
                payout = self.player.amount * 2
            elif self.player.bet_on == "莊家":
                payout = self.player.amount + math.floor(self.player.amount * 0.95)
            elif self.player.bet_on == "和局":
                payout = self.player.amount * 9
        elif self.game_winner == "和局" and self.player.bet_on in ["閒家", "莊家"]:
            status, payout = "平局", self.player.amount

        self.payout = payout
        self.status = status
        self.profit = self.payout - self.player.amount

    def to_stats_dict(self) -> Dict[str, Any]:
        """生成用於儲存到 game_stats_service 的標準字典"""
        return {
            "bet": self.player.amount,
            "payout": self.payout,
            "profit": self.profit,
            "result": (
                "win" if self.profit > 0 else "lose" if self.profit < 0 else "push"
            ),
            "bet_on": self.player.bet_on,
            "original_result": self.game_winner,
            "player_hand": self.player_hand.to_short_string(),
            "player_hand_value": self.player_hand.value,
            "banker_hand": self.banker_hand.to_short_string(),
            "banker_hand_value": self.banker_hand.value,
        }


@dataclass
class BaccaratGame:
    game_id: str
    host_id: int
    message: Optional[discord.Message] = None
    state: Literal["betting", "playing", "finished"] = "betting"
    players: Dict[int, Player] = field(default_factory=dict)
    player_hand: Hand = field(default_factory=Hand)
    banker_hand: Hand = field(default_factory=Hand)
    deck: Deck = field(default_factory=Deck)
    winner: Optional[Literal["閒家", "莊家", "和局"]] = None

    def _should_banker_draw(self, p_drew: bool, p3_val: Optional[int]) -> bool:
        """根據百家樂規則判斷莊家是否需要補第三張牌"""
        b_score = self.banker_hand.value
        if not p_drew:
            return b_score <= 5
        if b_score <= 2:
            return True
        if b_score == 3 and p3_val != 8:
            return True
        if b_score == 4 and p3_val in range(2, 8):
            return True
        if b_score == 5 and p3_val in range(4, 8):
            return True
        if b_score == 6 and p3_val in [6, 7]:
            return True
        return False

    def play(self):
        """以生成器(generator)的方式執行百家樂遊戲流程。"""
        card1 = self.deck.deal()
        if card1 is not None:
            self.player_hand.add_card(card1)
        yield "player_card_1"
        card2 = self.deck.deal()
        if card2 is not None:
            self.banker_hand.add_card(card2)
        yield "banker_card_1"
        card3 = self.deck.deal()
        if card3 is not None:
            self.player_hand.add_card(card3)
        yield "player_card_2"
        card4 = self.deck.deal()
        if card4 is not None:
            self.banker_hand.add_card(card4)
        yield "banker_card_2"

        p_score, b_score = self.player_hand.value, self.banker_hand.value
        if p_score >= 8 or b_score >= 8:
            yield "natural_win"
            return

        p_drew, p3_val = False, None
        if p_score <= 5:
            card = self.deck.deal()
            if card:
                self.player_hand.add_card(card)
                p_drew, p3_val = True, card.baccarat_value
                yield "player_third_card"

        if self._should_banker_draw(p_drew, p3_val):
            banker_card = self.deck.deal()
            if banker_card is not None:
                self.banker_hand.add_card(banker_card)
            yield "banker_third_card"

        yield "final_result"

    def _determine_winner(self):
        if self.winner:
            return
        p_score, b_score = self.player_hand.value, self.banker_hand.value
        if p_score > b_score:
            self.winner = "閒家"
        elif b_score > p_score:
            self.winner = "莊家"
        else:
            self.winner = "和局"

    def calculate_results(self) -> List[BaccaratResult]:
        """
        完成遊戲結算，確定贏家並為每個玩家生成結果對象。
        """
        self._determine_winner()
        self.state = "finished"

        results = []
        for player in self.players.values():
            result = BaccaratResult(
                player=player,
                game_winner=self.winner or "和局",  # Provide default value
                player_hand=self.player_hand,
                banker_hand=self.banker_hand,
            )
            results.append(result)
        return results


# --- Discord UI 元件 ---


class BetModal(BaseModal):
    def __init__(self, cog: "BaccaratCog", game_id: str, bet_on: str):
        super().__init__(bot=cog.bot, title="下注金額", timeout=120)
        self.cog, self.game_id, self.bet_on = cog, game_id, bet_on
        self.amount_input = TextInput(
            label=f"您選擇了押【{bet_on}】",
            placeholder="請輸入您的下注金額",
            required=True,
        )
        self.add_item(self.amount_input)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        amount = int(self.amount_input.value)
        if amount <= 0:
            raise InvalidBetAmountError("下注金額必須是有效的正整數。")

        user_id = interaction.user.id
        balance_info = await economy_service.get_balance(user_id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < amount:
            raise InsufficientBalanceError(required=amount, current=current_balance)

        lock = self.cog.game_locks.get(self.game_id)
        if not lock:
            raise GameError("牌局不存在或已結束！")

        async with lock:
            game = self.cog.active_games.get(self.game_id)
            if not game or game.state != "betting":
                raise GameError("抱歉，下注時間已截止！")

            if user_id in game.players:
                await economy_service.award_oil(
                    user_id=user_id,
                    amount=game.players[user_id].amount,
                    transaction_type="game:baccarat_refund",
                    reason="Baccarat re-bet refund",
                )
            await economy_service.award_oil(
                user_id=user_id,
                amount=-amount,
                transaction_type="game:baccarat_bet",
                reason=f"Baccarat bet on {self.bet_on}",
            )

            # Ensure bet_on is a valid literal
            valid_bet_on = (
                self.bet_on if self.bet_on in ["閒家", "莊家", "和局"] else "閒家"
            )
            player = Player(
                user_id=user_id,
                user_name=interaction.user.display_name,
                bet_on=valid_bet_on,  # type: ignore
                amount=amount,
            )
            game.players[user_id] = player
            embed = SuccessEmbed(
                description=f"您已成功下注 **{amount}** 在 **{self.bet_on}**！"
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            if self.game_id not in self.cog.update_queues:
                self.cog.update_queues[self.game_id] = asyncio.Queue()
            await self.cog.update_queues[self.game_id].put(True)


class ReplayBetModal(BaseModal):
    def __init__(
        self, cog: "BaccaratCog", bet_on: str, last_amount: int, previous_view: View
    ):
        super().__init__(bot=cog.bot, title="再來一局 - 輸入金額", timeout=120)
        self.cog = cog
        self.bet_on = bet_on
        self.previous_view = previous_view
        self.amount_input = TextInput(
            label=f"您選擇了押【{bet_on}】",
            placeholder="請輸入您的下注金額",
            default=str(last_amount),
            required=True,
        )
        self.add_item(self.amount_input)

    async def on_submit(self, interaction: discord.Interaction):
        amount = int(self.amount_input.value)
        if amount <= 0:
            raise InvalidBetAmountError("請輸入有效的正整數金額！")

        # 檢查餘額
        balance_info = await economy_service.get_balance(interaction.user.id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < amount:
            raise InsufficientBalanceError(required=amount, current=current_balance)

        # 將 previous_view 傳遞給 start_new_game
        await self.cog.start_new_game(
            interaction, amount, self.bet_on, previous_view=self.previous_view
        )


class GameView(BaseView):
    def __init__(self, cog: "BaccaratCog", game_id: str, host_id: int):
        # 1. 將超時時間改為 90 秒
        super().__init__(bot=cog.bot, user_id=host_id, timeout=90.0)
        self.cog, self.game_id = cog, game_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        覆寫基礎檢查，允許所有玩家點擊下注按鈕。
        「開始遊戲」按鈕有自己的內部權限檢查，所以這是安全的。
        """
        return True

    async def on_timeout(self):
        """
        當View在90秒後超時，自動開始遊戲。
        """
        logger.info("遊戲 %s 的 View 已達 90 秒，觸發自動開始。", self.game_id)
        self.stop()  # 停止監聽，防止後續交互

        game = self.cog.active_games.get(self.game_id)

        # 2. 檢查遊戲狀態，如果遊戲已經開始或結束，就什麼都不做
        if not game or game.state != "betting":
            logger.warning("遊戲 %s 超時，但狀態不是 'betting'，已跳過。", self.game_id)
            return

        # 因為遊戲創建時必定有玩家，所以直接執行開始遊戲邏輯
        logger.info("遊戲 %s 正在自動開始...", self.game_id)

        # 我們需要一個 'Interaction' 對象來傳遞給 resolve_game。
        # 由於我們是在超時後觸發，沒有現成的 interaction。
        # 我們可以從遊戲訊息(game.message)中獲取必要的信息。
        if game.message:
            # 創建一個模擬的 Interaction
            class FakeInteraction:
                # 接收 cog 作為參數
                def __init__(self, message: discord.Message, cog: "BaccaratCog"):
                    self.message = message
                    self.channel = message.channel
                    guild = (
                        cog.bot.get_guild(message.guild.id) if message.guild else None
                    )
                    self.user = guild.get_member(game.host_id) if guild else None

                    if self.user is None:
                        # 使用傳入的 cog 來獲取 bot.user
                        self.user = cog.bot.user

                    self.response = self

                async def defer(self):
                    pass

                def is_done(self):
                    return True

            # 實例化時傳入 self.cog
            fake_interaction = FakeInteraction(game.message, self.cog)
            # 類型忽略，因為 FakeInteraction 是模擬對象
            await self.cog.resolve_game(fake_interaction, self.game_id)  # type: ignore
        else:
            # 如果連 message 都沒有，說明遊戲狀態有問題，直接清理
            logger.error(
                f"遊戲 {self.game_id} 超時但找不到訊息對象，無法自動開始。正在清理..."
            )
            await self.cog._handle_game_cancellation(game)  # 帶退款的清理

    async def _handle_bet(self, interaction: discord.Interaction, bet_on: str):
        game = self.cog.active_games.get(self.game_id)
        if not game or game.state != "betting":
            raise GameError("這場牌局已結束或無法下注！")
        await interaction.response.send_modal(BetModal(self.cog, self.game_id, bet_on))

    @discord.ui.button(label="押閒家", style=discord.ButtonStyle.primary)
    async def player_button(self, i: discord.Interaction, b: Button):
        await self._handle_bet(i, "閒家")

    @discord.ui.button(label="押莊家", style=discord.ButtonStyle.danger)
    async def banker_button(self, i: discord.Interaction, b: Button):
        await self._handle_bet(i, "莊家")

    @discord.ui.button(label="押和局", style=discord.ButtonStyle.secondary)
    async def tie_button(self, i: discord.Interaction, b: Button):
        await self._handle_bet(i, "和局")

    @discord.ui.button(label="▶️ 開始遊戲", style=discord.ButtonStyle.success)
    async def start_button(self, i: discord.Interaction, b: Button):
        game = self.cog.active_games.get(self.game_id)
        if not game or i.user.id != game.host_id:
            raise InvalidOperationError("只有房主才能開始遊戲！")

        # 關鍵：手動點擊時，停止 View 的監聽，這樣 on_timeout 就不會被觸發了
        self.stop()

        await self.cog.resolve_game(i, self.game_id)


class ReplayView(BaseView):
    def __init__(
        self, cog: "BaccaratCog", last_bet: int, host_id: int, message: discord.Message
    ):
        super().__init__(bot=cog.bot, user_id=host_id, timeout=180)
        self.cog = cog
        self.last_bet = last_bet
        # self.host_id is now stored in self.user_id from BaseView
        self.message = message  # 存儲View所附加的消息，以便超時後編輯

    async def _handle_replay(self, interaction: discord.Interaction, bet_on: str):
        # The permission check is now handled by BaseView.interaction_check
        # 傳遞 self (ReplayView 實例) 給 Modal，以便後續禁用
        modal = ReplayBetModal(self.cog, bet_on, self.last_bet, previous_view=self)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="押閒家 (新局)", style=discord.ButtonStyle.primary)
    async def player_button(self, i: discord.Interaction, b: Button):
        await self._handle_replay(i, "閒家")

    @discord.ui.button(label="押莊家 (新局)", style=discord.ButtonStyle.danger)
    async def banker_button(self, i: discord.Interaction, b: Button):
        await self._handle_replay(i, "莊家")

    @discord.ui.button(label="押和局 (新局)", style=discord.ButtonStyle.secondary)
    async def tie_button(self, i: discord.Interaction, b: Button):
        await self._handle_replay(i, "和局")


# --- 主要的 COG 類 ---


class BaccaratCog(commands.Cog, name="baccarat"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.active_games: Dict[str, BaccaratGame] = {}
        self.game_locks: Dict[str, asyncio.Lock] = {}
        self.update_queues: Dict[str, asyncio.Queue] = {}
        self.message_update_task.start()

    async def cog_unload(self):
        self.message_update_task.cancel()

    async def start_new_game(
        self,
        interaction: discord.Interaction,
        amount: int,
        on: str,
        previous_view: Optional[View] = None,
    ):
        if not interaction.response.is_done():
            await interaction.response.defer()

        # 如果是從 ReplayView 過來的，先禁用舊的 View
        if previous_view:
            for item in previous_view.children:
                if hasattr(item, "disabled"):
                    item.disabled = True  # type: ignore
            if hasattr(previous_view, "message") and previous_view.message:  # type: ignore
                await previous_view.message.edit(view=previous_view)  # type: ignore
            previous_view.stop()

        user_id = interaction.user.id
        balance_info = await economy_service.get_balance(user_id)
        current_balance = balance_info.get("balance", 0)
        if current_balance < amount:
            raise InsufficientBalanceError(required=amount, current=current_balance)

        await economy_service.award_oil(
            user_id=user_id,
            amount=-amount,
            transaction_type="game:baccarat_bet",
            reason=f"Baccarat bet on {on}",
        )
        game_id = f"bacc_{user_id}_{int(time.time() * 1000)}"
        game = BaccaratGame(game_id=game_id, host_id=user_id, deck=Deck(num_decks=8))
        self.game_locks[game_id] = asyncio.Lock()
        async with self.game_locks[game_id]:
            # Ensure bet_on is a valid literal
            valid_bet_on = on if on in ["閒家", "莊家", "和局"] else "閒家"
            player = Player(
                user_id=user_id,
                user_name=interaction.user.display_name,
                bet_on=valid_bet_on,  # type: ignore
                amount=amount,
            )
            game.players[user_id] = player
            self.active_games[game_id] = game
            view = GameView(self, game_id, user_id)
            embed = await self._create_game_embed(game)
            # Let exceptions bubble up to the global error handler
            message = await interaction.followup.send(embed=embed, view=view, wait=True)
            game.message = message

    @app_commands.command(name="baccarat", description="發起一場新的百家樂牌局")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(amount="您想下的第一筆賭注金額", on="您想押注的對象")
    @app_commands.choices(
        on=[
            app_commands.Choice(name="閒家", value="閒家"),
            app_commands.Choice(name="莊家", value="莊家"),
            app_commands.Choice(name="和局", value="和局"),
        ]
    )
    async def baccarat(
        self,
        interaction: discord.Interaction,
        amount: app_commands.Range[int, 1],
        on: Literal["閒家", "莊家", "和局"],
    ):
        await self.start_new_game(interaction, amount, on)

    async def _get_user_stats(self, user_id: int) -> Optional[Dict[str, Any]]:
        """獲取用戶在百家樂的統計數據"""
        stats = await game_stats_service.get_user_game_stats(user_id, "baccarat")
        return stats

    async def _create_game_embed(
        self, game: BaccaratGame, results: Optional[Dict] = None
    ) -> discord.Embed:
        if results is None:
            results = {}
        state = game.state
        color_map = {
            "閒家": discord.Color.green(),
            "莊家": discord.Color.red(),
            "和局": discord.Color.light_grey(),
        }

        if state == "betting":
            author_name, color = "🎲 百家樂牌局 - 等待下注", discord.Color.gold()
            desc = ["> 賠率：閒家 1:1 | 莊家 1:0.95 (含5%佣金) | 和局 1:8"]
        elif state == "playing":
            author_name, color, desc = "發牌中...下注已鎖定！", discord.Color.blue(), []
        else:  # finished
            author_name = f"🎉 遊戲結束 - {game.winner}獲勝！"
            color = color_map.get(game.winner or "", discord.Color.blurple())
            desc = [
                f"# **莊家 ({game.banker_hand.value}點):**",
                f"# {game.banker_hand}",
                "",
                f"# **閒家 ({game.player_hand.value}點):**",
                f"# {game.player_hand}",
                "\n**玩家結算:**",
            ]
            for uid, res in results.items():
                profit_str = (
                    f"+{res['profit']}" if res["profit"] >= 0 else str(res["profit"])
                )
                desc.append(
                    f"<@{uid}>: {res['status']} <:oi:1382174314811363470>{profit_str}"
                )

                # 獲取並添加統計數據
                stats = await self._get_user_stats(uid)
                if stats:
                    total_profit = stats.get("total_profit_loss", 0)
                    total_games = stats.get("total_games", 0)
                    total_wins = stats.get("total_wins", 0)
                    win_rate = (
                        round((total_wins / total_games) * 100, 1)
                        if total_games > 0
                        else 0
                    )

                    profit_sign = "+" if total_profit >= 0 else ""
                    stats_line = f"<:Reply:1357534074830590143> 總盈虧: {profit_sign}{total_profit} | 場次: {total_games} | 勝率: {win_rate}%"
                    desc.append(stats_line)

        if state != "finished":
            desc.append("\n**玩家列表:**")
            if not game.players:
                desc.append("目前沒有玩家。")
            else:
                for p in game.players.values():
                    desc.append(
                        f"- {p.user_name}: 下注 **{p.amount}** 在 **{p.bet_on}**"
                    )

        embed = discord.Embed(description="\n".join(desc), color=color)
        embed.set_author(name=author_name)
        embed.set_thumbnail(url=BACCARAT_IMAGE_URL)

        # 根據遊戲狀態設置不同的footer文字
        if state == "finished":
            footer_text = "點擊下方按鈕即可再來一局！"
        else:
            footer_text = "其他玩家可跟著下注！將在90秒後或房主點擊後自動開始。"

        embed.set_footer(
            text=footer_text,
            icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1389336470980136970/arrow.png?ex=68643feb&is=6862ee6b&hm=5d8ff93c6acbb7ce6246efa515f6291cf1f04b7b3302bcac9b227daa16109c5d&",
        )
        return embed

    async def _handle_update_exception(self, e: discord.HTTPException, game_id: str):
        """處理 message_update_task 中的 HTTP 異常"""
        error_messages = {
            401: f"更新遊戲 {game_id} 訊息失敗 (HTTP 401 - Invalid Webhook Token): {e}\n401 錯誤不可恢復，清理遊戲以避免觸發 Discord IP 限制",
            404: f"試圖更新遊戲 {game_id} 的訊息時未找到該訊息。",
            403: f"沒有權限更新遊戲 {game_id} 的訊息，可能權限已被移除。",
        }
        if e.status in error_messages:
            logger.error(error_messages[e.status])
            await self._cleanup_game(game_id)
        else:
            logger.error(
                "更新遊戲 %s 訊息時發生 HTTP 錯誤 (%s): %s", game_id, e.status, e
            )

    async def _process_game_update(self, game_id: str):
        """處理單個遊戲的訊息更新邏輯"""
        async with self.game_locks[game_id]:
            game = self.active_games.get(game_id)
            if not game or not game.message or game.state != "betting":
                if game_id in self.update_queues:
                    del self.update_queues[game_id]
                return

            try:
                embed = await self._create_game_embed(game)
                await game.message.edit(embed=embed)
            except discord.HTTPException as e:
                await self._handle_update_exception(e, game_id)
            except Exception as e:
                logger.error(
                    f"更新遊戲 {game_id} 訊息時發生未知錯誤: {e}", exc_info=True
                )

    @tasks.loop(seconds=1.5)
    async def message_update_task(self):
        for game_id, queue in list(self.update_queues.items()):
            if queue.empty():
                continue
            while not queue.empty():
                try:
                    queue.get_nowait()
                except asyncio.QueueEmpty:
                    break

            if game_id in self.game_locks:
                await self._process_game_update(game_id)
            elif game_id in self.update_queues:
                del self.update_queues[game_id]

    def _create_reveal_embed(self, game: BaccaratGame) -> discord.Embed:
        """創建開牌過程中的 embed"""
        author_name = "🃏 開牌中..."
        color = discord.Color.blue()

        desc = [
            f"# **莊家 ({game.banker_hand.value}點):**",
            f"# {game.banker_hand if game.banker_hand.cards else '尚未發牌'}",
            "",
            f"# **閒家 ({game.player_hand.value}點):**",
            f"# {game.player_hand if game.player_hand.cards else '尚未發牌'}",
        ]

        embed = discord.Embed(description="\n".join(desc), color=color)
        embed.set_author(name=author_name)
        embed.set_thumbnail(url=BACCARAT_IMAGE_URL)
        embed.set_footer(
            text="下注已鎖定，正在開牌中...",
            icon_url="https://cdn.discordapp.com/attachments/1336020673730187334/1389336470980136970/arrow.png?ex=68643feb&is=6862ee6b&hm=5d8ff93c6acbb7ce6246efa515f6291cf1f04b7b3302bcac9b227daa16109c5d&",
        )
        return embed

    async def _start_game_ui(
        self, interaction: discord.Interaction, game: BaccaratGame
    ) -> bool:
        """處理開始遊戲時的UI更新，成功返回True，失敗返回False"""
        game.state = "playing"
        if not game.message:
            try:
                game.message = await interaction.original_response()
            except (discord.NotFound, discord.HTTPException):
                logger.warning("無法獲取 %s 的原始訊息，嘗試發送新訊息。", game.game_id)
                try:
                    if hasattr(interaction.channel, "send"):
                        game.message = await interaction.channel.send("正在開始遊戲...")  # type: ignore
                    else:
                        logger.error("頻道 %s 不支持發送消息", interaction.channel)
                        return False
                except discord.HTTPException as e:
                    logger.error("無法在頻道 %s 中發送訊息: %s", interaction.channel, e)
                    return False

        embed = await self._create_game_embed(game)
        try:
            await game.message.edit(embed=embed, view=None)
            return True
        except discord.HTTPException as e:
            error_map = {
                401: "Invalid Webhook Token",
                403: "缺少編輯訊息的權限",
                404: "遊戲訊息被刪除",
            }
            if e.status in error_map:
                logger.error(
                    f"無法開始遊戲 {game.game_id} (HTTP {e.status} - {error_map[e.status]}): {e}"
                )
            else:
                logger.error(
                    f"編輯訊息以開始遊戲 {game.game_id} 時失敗 (HTTP {e.status}): {e}"
                )
            return False

    async def _run_game_loop(self, game: BaccaratGame) -> bool:
        """執行遊戲開牌動畫迴圈，如果UI更新失敗返回True"""
        game_generator = game.play()
        for _ in game_generator:
            embed = self._create_reveal_embed(game)
            try:
                if game.message:
                    await game.message.edit(embed=embed)
                await asyncio.sleep(DEAL_DELAY)
            except discord.HTTPException as e:
                logger.error(
                    f"遊戲 {game.game_id} 進行中UI更新失敗: {e}. 遊戲將在後台繼續結算。"
                )
                return True  # 表示UI更新失敗
        return False

    async def _finalize_and_payout(self, game: BaccaratGame):
        """處理遊戲結算、獎金支付和數據統計"""
        game_results = game.calculate_results()
        payout_tasks = []
        stats_tasks = []
        embed_results = {}

        for res in game_results:
            embed_results[res.player.user_id] = {
                "profit": res.profit,
                "status": res.status,
            }
            if res.payout > 0:
                payout_tasks.append(
                    economy_service.award_oil(
                        user_id=res.player.user_id,
                        amount=res.payout,
                        transaction_type="game:baccarat_win",
                        reason=f"Baccarat win on {res.player.bet_on}",
                    )
                )
            stats_tasks.append(
                game_stats_service.record_game_result(
                    res.player.user_id, "baccarat", res.to_stats_dict()
                )
            )

        try:
            await asyncio.gather(*payout_tasks)
        except Exception as e:
            logger.error("遊戲 %s 批量發放獎勵時出錯: %s", game.game_id, e)
        try:
            await asyncio.gather(*stats_tasks)
        except Exception as e:
            logger.error("遊戲 %s 批量記錄統計時出錯: %s", game.game_id, e)

        return embed_results

    async def _update_final_ui(
        self,
        interaction: discord.Interaction,
        game: BaccaratGame,
        embed_results: Dict,
        ui_update_failed: bool,
    ):
        """更新遊戲結束後的最終UI"""
        last_bet = (
            game.players[game.host_id].amount if game.host_id in game.players else 10
        )
        embed = await self._create_game_embed(game, embed_results)

        if not ui_update_failed and game.message:
            try:
                await game.message.edit(embed=embed, view=None)
                view = ReplayView(self, last_bet, game.host_id, game.message)
                await game.message.edit(view=view)
            except discord.HTTPException as e:
                logger.warning("遊戲 %s 最終結果發送失敗: %s", game.game_id, e)
                try:
                    if hasattr(interaction.channel, "send"):
                        await interaction.channel.send(  # type: ignore
                            content=f"百家樂遊戲 `{game.game_id}` 已結束。", embed=embed
                        )
                except discord.HTTPException:
                    logger.error("無法為遊戲 %s 發送最終結果的新消息。", game.game_id)
        else:
            logger.info(
                "遊戲 %s 在UI失敗後完成後台結算，嘗試發送新消息。", game.game_id
            )
            try:
                if hasattr(interaction.channel, "send"):
                    await interaction.channel.send(  # type: ignore
                        content=f"百家樂遊戲 `{game.game_id}` 已結束。", embed=embed
                    )
            except discord.HTTPException:
                logger.error("無法為遊戲 %s 發送最終結果的新消息。", game.game_id)

    async def resolve_game(self, interaction: discord.Interaction, game_id: str):
        if not interaction.response.is_done():
            await interaction.response.defer()

        lock = self.game_locks.get(game_id)
        if not lock:
            raise GameError("牌局不存在或已結束！")

        async with lock:
            game = self.active_games.get(game_id)
            if not game or game.state != "betting":
                return

            if not await self._start_game_ui(interaction, game):
                await self._handle_game_cancellation(game)
                return

            await asyncio.sleep(DEAL_DELAY)
            ui_update_failed = await self._run_game_loop(game)
            embed_results = await self._finalize_and_payout(game)
            await self._update_final_ui(
                interaction, game, embed_results, ui_update_failed
            )

        await self._cleanup_game(game_id)

    async def _handle_game_cancellation(self, game: BaccaratGame):
        """處理遊戲因故取消時的退款和清理工作"""
        logger.info("正在取消遊戲 %s 並退款...", game.game_id)
        for player in game.players.values():
            try:
                await economy_service.award_oil(
                    user_id=player.user_id,
                    amount=player.amount,
                    transaction_type="game:baccarat_refund",
                    reason="Baccarat game cancelled refund",
                )
            except Exception as e:
                logger.error("為玩家 %s 退款時失敗: %s", player.user_id, e)
        await self._cleanup_game(game.game_id)

    async def _cleanup_game(self, game_id: str):
        if game_id in self.active_games:
            del self.active_games[game_id]
        if game_id in self.game_locks:
            del self.game_locks[game_id]
        if game_id in self.update_queues:
            del self.update_queues[game_id]
        logger.info("已清理遊戲 %s", game_id)


async def setup(bot: commands.Bot):
    await bot.add_cog(BaccaratCog(bot))
    logger.info("BaccaratCog 已成功加載。")
