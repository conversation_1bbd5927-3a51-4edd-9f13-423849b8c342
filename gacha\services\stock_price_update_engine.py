import json
import random
from decimal import Decimal

from config.app_config import get_gacha_stock_integration_config
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.models.market_models import StockLifecycleStatus
from gacha.services import anchor_price_service
from utils.logger import logger


async def update_stock_prices():
    """
    核心股價更新引擎。此函數作為一個獨立的、無狀態的服務運行，
    負責計算和更新所有活躍股票的價格。
    """
    logger.info("開始執行股價更新任務。")

    # 從 get_gacha_stock_integration_config 加載配置
    stock_market_config = get_gacha_stock_integration_config().stock_market
    min_asset_price = Decimal(
        str(getattr(stock_market_config, "global_min_asset_price", "1.0"))
    )
    # news_relevance_hours = stock_market_config.news_relevance_window_hours  # Currently unused
    # max_price_change_percent_config = Decimal(
    #     str(getattr(stock_market_config, "max_price_change_percent", "0.2"))
    # )  # Currently unused
    global_max_asset_price_config = Decimal(
        str(getattr(stock_market_config, "global_max_asset_price", "1000000"))
    )
    global_market_volatility_factor = Decimal(
        str(getattr(stock_market_config, "global_market_volatility_factor", "1.0"))
    )
    bottom_suppression_config = stock_market_config.bottom_price_suppression
    # bottom_suppression_threshold_factor = Decimal(
    #     str(bottom_suppression_config.threshold_factor)
    # )  # Currently unused
    # bottom_upward_momentum_suppression_factor = Decimal(
    #     str(bottom_suppression_config.upward_momentum_suppression_factor)
    # )  # Currently unused
    # bottom_downward_pressure_chance = Decimal(
    #     str(bottom_suppression_config.downward_pressure_chance)
    # )  # Currently unused
    # bottom_downward_pressure_percent = Decimal(
    #     str(bottom_suppression_config.downward_pressure_percent)
    # )  # Currently unused

    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        assets_query = """
            SELECT asset_id, current_price, base_volatility, volatility_factor,
                   initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                   lifecycle_status
            FROM virtual_assets
            WHERE (current_price > 0 OR current_price IS NULL)
                  AND (initial_anchor_price IS NOT NULL OR current_anchor_price IS NOT NULL)
                  AND lifecycle_status != $1
        """
        assets = await conn.fetch(assets_query, StockLifecycleStatus.DELISTED.value)
        if not assets:
            logger.info("No assets found to update prices for.")
            return

        updated_count = 0
        for asset in assets:
            asset_id = asset["asset_id"]
            current_price = (
                Decimal(str(asset["current_price"]))
                if asset["current_price"] is not None
                else min_asset_price
            )
            base_volatility = Decimal(str(asset["base_volatility"]))
            volatility_factor = Decimal(str(asset["volatility_factor"]))
            asset_lifecycle_status = (
                StockLifecycleStatus(asset["lifecycle_status"])
                if asset["lifecycle_status"]
                else StockLifecycleStatus.ACTIVE
            )

            effective_anchor_price = (
                await anchor_price_service.get_effective_anchor_price(
                    conn,
                    asset_id,
                    asset["initial_anchor_price"],
                    asset["current_anchor_price"],
                    asset["anchor_price_updated_at"],
                )
            )

            if effective_anchor_price <= Decimal("0"):
                logger.error(
                    "AssetID %s: Effective anchor price is %s. Skipping price update.",
                    asset_id,
                    effective_anchor_price,
                )
                continue

            try:
                pending_news_redis_key = f"pending_news_impacts:{asset_id}"
                redis_client = get_redis_client()
                assert redis_client is not None, "Redis client is not initialized"
                pending_events_json_list = await redis_client.lrange(  # type: ignore
                    pending_news_redis_key, 0, -1
                )
                total_queued_news_impact_amount = Decimal("0.0")
                if pending_events_json_list:
                    await redis_client.ltrim(  # type: ignore
                        pending_news_redis_key, len(pending_events_json_list), -1
                    )
                    for event_json_str in pending_events_json_list:
                        try:
                            event_data = json.loads(event_json_str)
                            impact_amount = Decimal(
                                event_data.get("impact_amount_str", "0.0")
                            )
                            total_queued_news_impact_amount += impact_amount
                            logger.info(
                                "AssetID %s: Applied queued news impact. NewsID: %s, Amount: %s",
                                asset_id,
                                event_data.get("news_id"),
                                impact_amount,
                            )
                        except json.JSONDecodeError:
                            logger.error(
                                "AssetID %s: Failed to decode pending news event JSON: %s",
                                asset_id,
                                event_json_str,
                                exc_info=True,
                            )
                        except Exception as e_event:
                            logger.error(
                                "AssetID %s: Error processing pending news event: %s, Error: %s",
                                asset_id,
                                event_json_str,
                                e_event,
                                exc_info=True,
                            )

                random_walk_percent_float = random.normalvariate(
                    0, float(base_volatility)
                )
                random_walk_amount = current_price * Decimal(
                    str(random_walk_percent_float)
                )

                news_impact_raw = (
                    total_queued_news_impact_amount / volatility_factor
                    if volatility_factor > 0
                    else Decimal("0.0")
                )
                total_change_amount_raw = random_walk_amount + news_impact_raw
                total_change_amount_amplified_unlimited = (
                    total_change_amount_raw
                    * volatility_factor
                    * global_market_volatility_factor
                )

                abs_max_change_percent = Decimal("0.05")
                final_max_abs_change_amount = current_price * abs_max_change_percent
                logger.info(
                    "資產ID %s：價格變動限制基於當前價格=%s，5%%限制=%s",
                    asset_id,
                    current_price,
                    final_max_abs_change_amount,
                )

                total_change_amount_amplified = total_change_amount_amplified_unlimited
                if (
                    abs(total_change_amount_amplified_unlimited)
                    > final_max_abs_change_amount
                ):
                    total_change_amount_amplified = (
                        final_max_abs_change_amount
                        if total_change_amount_amplified_unlimited > 0
                        else -final_max_abs_change_amount
                    )
                    logger.info(
                        "AssetID %s: Price change amount %s (raw %s from anchor %s) hit final limit, capped at %s.",
                        asset_id,
                        total_change_amount_amplified_unlimited,
                        total_change_amount_raw,
                        effective_anchor_price,
                        total_change_amount_amplified,
                    )

                active_bottom_suppression_config = bottom_suppression_config
                is_st_suppression_active = False
                if (
                    asset_lifecycle_status == StockLifecycleStatus.ST
                    and stock_market_config.st_bottom_price_suppression
                ):
                    active_bottom_suppression_config = (
                        stock_market_config.st_bottom_price_suppression
                    )
                    is_st_suppression_active = True
                    logger.info(
                        "AssetID %s: Stock is ST, using ST-specific bottom suppression config.",
                        asset_id,
                    )

                current_suppression_threshold_factor = Decimal(
                    str(active_bottom_suppression_config.threshold_factor)
                )
                current_upward_momentum_suppression_factor = Decimal(
                    str(
                        active_bottom_suppression_config.upward_momentum_suppression_factor
                    )
                )
                current_downward_pressure_chance = Decimal(
                    str(active_bottom_suppression_config.downward_pressure_chance)
                )
                current_downward_pressure_percent = Decimal(
                    str(active_bottom_suppression_config.downward_pressure_percent)
                )

                suppression_trigger_price = (
                    min_asset_price * current_suppression_threshold_factor
                )
                if current_price < suppression_trigger_price:
                    log_prefix = f"AssetID {asset_id} (Status: {asset_lifecycle_status.value}, ST Suppression: {is_st_suppression_active})"
                    logger.info(
                        "%s: Price %s is below suppression threshold %s. Applying bottom suppression logic.",
                        log_prefix,
                        current_price,
                        suppression_trigger_price,
                    )
                    if total_change_amount_amplified > 0:
                        original_upward_change = total_change_amount_amplified
                        total_change_amount_amplified *= (
                            current_upward_momentum_suppression_factor
                        )
                        logger.info(
                            "%s: Upward momentum suppressed from %s to %s using factor %s.",
                            log_prefix,
                            original_upward_change,
                            total_change_amount_amplified,
                            current_upward_momentum_suppression_factor,
                        )
                    if random.random() < float(current_downward_pressure_chance):
                        downward_pressure_amount = (
                            current_price * current_downward_pressure_percent
                        )
                        total_change_amount_amplified -= downward_pressure_amount
                        logger.info(
                            "%s: Applied additional downward pressure of %s (chance %s, percent %s). New total change: %s.",
                            log_prefix,
                            downward_pressure_amount,
                            current_downward_pressure_chance,
                            current_downward_pressure_percent,
                            total_change_amount_amplified,
                        )

                new_price_raw = current_price + total_change_amount_amplified
                new_price_after_min_limit = max(min_asset_price, new_price_raw)
                final_new_price = min(
                    new_price_after_min_limit, global_max_asset_price_config
                )

                if (
                    new_price_after_min_limit > global_max_asset_price_config
                    and new_price_after_min_limit != final_new_price
                ):
                    logger.info(
                        "AssetID %s: Price %s hit global max price %s, capped at %s.",
                        asset_id,
                        new_price_after_min_limit,
                        global_max_asset_price_config,
                        final_new_price,
                    )

                if final_new_price != current_price:
                    async with conn.transaction():
                        await conn.execute(
                            "UPDATE virtual_assets SET current_price = $1, last_updated = CURRENT_TIMESTAMP WHERE asset_id = $2",
                            final_new_price,
                            asset_id,
                        )
                        await conn.execute(
                            "INSERT INTO asset_price_history (asset_id, price, timestamp) VALUES ($1, $2, CURRENT_TIMESTAMP)",
                            asset_id,
                            final_new_price,
                        )
                    updated_count += 1
                    change_percent_of_current = (
                        total_change_amount_amplified / current_price * 100
                        if current_price > 0
                        else Decimal("0")
                    )
                    logger.info(
                        "資產ID %s 價格更新：%s -> %s (%s%%)",
                        asset_id,
                        current_price,
                        final_new_price,
                        change_percent_of_current,
                    )
                else:
                    logger.info(
                        "AssetID %s: Price unchanged at %s. No update needed. Anchor: %s",
                        asset_id,
                        current_price,
                        effective_anchor_price,
                    )
            except Exception as e:
                logger.error(
                    "Error updating price for asset_id %s: %s",
                    asset_id,
                    e,
                    exc_info=True,
                )

        if updated_count > 0:
            logger.info("成功更新 %s 個資產的價格。", updated_count)
        else:
            logger.info("No asset prices were changed in this cycle.")
    logger.info("已完成股票價格更新任務。")
