"""
PlayerGlobalSkillProficiencyService - 玩家全局技能熟練度服務

負責玩家全局技能的學習、升級、獲取進度等操作。
"""

import json
import logging
from typing import Any, Dict, List, Literal, Optional

# 导入异常类 - 統一使用gacha系統的異常
from gacha.exceptions import (
    CardNotFoundError,
    DatabaseOperationError,
    EntityNotFoundError,
    SkillNotFoundError,
)
from gacha.models.filters import CollectionFilters
from rpg_system.config.loader import get_config_loader
from rpg_system.repositories import (
    global_skill_repository,
    player_collection_repository,
)

logger = logging.getLogger(__name__)


# 定义RPG系统专用异常
class InsufficientCardsError(EntityNotFoundError):
    """卡牌数量不足异常"""

    pass


async def unlock_skills_from_card(user_id: int, card_id: int) -> Dict[str, Any]:
    """
    從卡片解鎖全局技能（抽卡時自動調用）

    Args:
        user_id: 玩家ID
        card_id: 卡片ID

    Returns:
        解鎖結果信息

    Raises:
        SkillNotFoundError: 卡片配置不存在
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 1. 獲取卡片配置
        config_loader = get_config_loader()
        card_config = await config_loader.get_card_config(str(card_id))
        if not card_config:
            raise SkillNotFoundError(f"找不到卡片配置: {card_id}")

        # 2. 提取卡片上的所有技能
        all_skills = []

        # 主動技能
        active_skills = card_config.get("initial_equipped_active_skills", [])
        if isinstance(active_skills, list):
            for skill_id in active_skills:
                if skill_id:  # 排除空值
                    all_skills.append((skill_id, "ACTIVE"))

        # 被動技能
        passive_skills = card_config.get("initial_equipped_passives", {})
        if isinstance(passive_skills, dict):
            for skill_id in passive_skills.values():
                if skill_id:  # 排除空值
                    all_skills.append((skill_id, "PASSIVE"))

        # 固有技能（天賦技能）
        innate_skills = card_config.get("innate_skills", [])
        if isinstance(innate_skills, list):
            for skill_id in innate_skills:
                if skill_id:  # 排除空值
                    all_skills.append((skill_id, "PASSIVE"))  # 固有技能通常是被動的

        # 3. 批量解鎖技能
        unlocked_skills = []
        already_unlocked = []

        for skill_id, skill_type in all_skills:
            try:
                result = await _unlock_single_skill(user_id, skill_id, skill_type)
                if result["newly_unlocked"]:
                    unlocked_skills.append(result)
                else:
                    already_unlocked.append(result)
            except Exception as e:
                logger.warning("解鎖技能失敗: skill_id=%s, error=%s", skill_id, e)
                continue

        logger.info(
            "卡片 %s 技能解鎖完成: user_id=%s, 新解鎖=%s, 已擁有=%s",
            card_id,
            user_id,
            len(unlocked_skills),
            len(already_unlocked),
        )

        return {
            "card_id": card_id,
            "newly_unlocked": unlocked_skills,
            "already_unlocked": already_unlocked,
            "total_skills_processed": len(all_skills),
        }

    except SkillNotFoundError:
        raise
    except Exception as e:
        logger.error(
            "從卡片解鎖技能時發生錯誤: card_id=%s, error=%s",
            card_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"解鎖技能失敗: {str(e)}") from e


async def _unlock_single_skill(
    user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]
) -> Dict[str, Any]:
    """
    解鎖單個技能

    Args:
        user_id: 玩家ID
        skill_id: 技能ID
        skill_type: 技能類型

    Returns:
        解鎖結果

    Raises:
        SkillNotFoundError: 技能不存在
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        if skill_type not in ["ACTIVE", "PASSIVE"]:
            raise ValueError(f"無效的技能類型: {skill_type}")

        # 1. 檢查技能是否存在於配置中
        skill_config = await _get_skill_config(skill_id)
        if not skill_config:
            raise SkillNotFoundError(f"找不到技能配置: {skill_id}")

        # 2. 檢查玩家是否已解鎖此技能
        existing_skill = await global_skill_repository.get_skill_data(
            user_id, skill_id, skill_type
        )
        if existing_skill:
            return {
                "skill_id": skill_id,
                "skill_type": skill_type,
                "skill_name": skill_config.get("name", skill_id),
                "newly_unlocked": False,
                "current_level": existing_skill.skill_level,
                "current_xp": existing_skill.skill_xp,
            }

        # 3. 解鎖技能
        await global_skill_repository.learn_skill(
            user_id=user_id,
            skill_id=skill_id,
            skill_type=skill_type,
            initial_level=1,
            initial_xp=0,
        )

        logger.info("玩家 %s 解鎖了 %s 技能: %s", user_id, skill_type, skill_id)
        return {
            "skill_id": skill_id,
            "skill_type": skill_type,
            "skill_name": skill_config.get("name", skill_id),
            "newly_unlocked": True,
            "current_level": 1,
            "current_xp": 0,
        }

    except (ValueError, SkillNotFoundError):
        raise
    except Exception as e:
        logger.error(
            "解鎖技能時發生錯誤: skill_id=%s, error=%s", skill_id, e, exc_info=True
        )
        raise DatabaseOperationError(f"解鎖技能失敗: {str(e)}") from e


async def batch_unlock_skills(
    user_id: int,
    skills: List[tuple],  # [(skill_id, skill_type), ...]
) -> Dict[str, Any]:
    """
    批量解鎖技能

    Args:
        user_id: 玩家ID
        skills: 技能列表 [(skill_id, skill_type), ...]

    Returns:
        批量解鎖結果

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        unlocked_skills = []
        already_unlocked = []
        failed_skills = []

        for skill_id, skill_type in skills:
            try:
                result = await _unlock_single_skill(user_id, skill_id, skill_type)
                if result["newly_unlocked"]:
                    unlocked_skills.append(result)
                else:
                    already_unlocked.append(result)
            except Exception as e:
                logger.warning("批量解鎖技能失敗: skill_id=%s, error=%s", skill_id, e)
                failed_skills.append(
                    {
                        "skill_id": skill_id,
                        "skill_type": skill_type,
                        "error": str(e),
                    }
                )

        logger.info(
            "批量解鎖技能完成: user_id=%s, 新解鎖=%s, 已擁有=%s, 失敗=%s",
            user_id,
            len(unlocked_skills),
            len(already_unlocked),
            len(failed_skills),
        )

        return {
            "newly_unlocked": unlocked_skills,
            "already_unlocked": already_unlocked,
            "failed_skills": failed_skills,
            "total_processed": len(skills),
        }

    except Exception as e:
        logger.error(
            "批量解鎖技能時發生錯誤: user_id=%s, error=%s",
            user_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"批量解鎖技能失敗: {str(e)}") from e


async def upgrade_global_skill(
    user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]
) -> Dict[str, Any]:
    """
    升級全局技能（純異常模式）

    Args:
        user_id: 玩家ID
        skill_id: 技能ID
        skill_type: 技能類型

    Returns:
        升級結果信息

    Raises:
        SkillNotFoundError: 技能不存在或未學習
        ValueError: 經驗不足或已達最大等級
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 1. 獲取玩家已學習的技能數據
        learned_skill = await global_skill_repository.get_skill_data(
            user_id, skill_id, skill_type
        )
        if not learned_skill:
            raise SkillNotFoundError(f"尚未學習技能: {skill_id}")

        # 2. 獲取技能配置
        skill_config = await _get_skill_config(skill_id)
        if not skill_config:
            raise SkillNotFoundError(f"找不到技能配置: {skill_id}")

        current_level = learned_skill.skill_level
        current_xp = learned_skill.skill_xp
        max_level = skill_config.get("max_level", 10)

        # 3. 檢查是否達到最大等級
        if current_level >= max_level:
            raise ValueError(f"技能已達到最大等級: {max_level}")

        # 4. 檢查是否有足夠的經驗
        required_xp = await _calculate_xp_required_for_level(
            current_level + 1, skill_id
        )
        if current_xp < required_xp:
            raise ValueError(f"經驗不足: 需要 {required_xp}，當前 {current_xp}")

        # 5. 執行升級
        new_level = current_level + 1
        remaining_xp = current_xp - required_xp

        await global_skill_repository.level_up_skill(
            user_id=user_id,
            skill_id=skill_id,
            skill_type=skill_type,
            new_level=new_level,
            remaining_xp=remaining_xp,
        )

        logger.info("玩家 %s 的技能 %s 升級到 %s 級", user_id, skill_id, new_level)
        return {
            "skill_id": skill_id,
            "skill_type": skill_type,
            "skill_name": skill_config.get("name", skill_id),
            "old_level": current_level,
            "new_level": new_level,
            "remaining_xp": remaining_xp,
            "required_xp_for_next": (
                await _calculate_xp_required_for_level(new_level + 1, skill_id)
                if new_level < max_level
                else None
            ),
        }

    except (SkillNotFoundError, ValueError):
        raise
    except Exception as e:
        logger.error("升級全局技能時發生錯誤: %s", e, exc_info=True)
        raise DatabaseOperationError(f"升級技能失敗: {str(e)}") from e


async def add_skill_experience(
    user_id: int,
    skill_id: str,
    skill_type: Literal["ACTIVE", "PASSIVE"],
    xp_amount: int,
) -> Optional[Dict[str, Any]]:
    """
    為已解鎖的全局技能增加經驗（純異常模式）

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        skill_type: 技能類型
        xp_amount: 經驗值數量

    Returns:
        Dict[str, Any]: 升級結果（如果升級）或經驗增加結果

    Raises:
        ValueError: 經驗值無效
        SkillNotFoundError: 技能不存在或未解鎖
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        if xp_amount <= 0:
            raise ValueError("經驗值必須大於0")

        # 1. 檢查技能配置是否存在
        config_loader = get_config_loader()
        if skill_type == "ACTIVE":
            skill_config = config_loader.get_active_skill_config(skill_id)
        else:
            skill_config = config_loader.get_passive_skill_config(skill_id)

        if not skill_config:
            raise SkillNotFoundError(f"找不到技能配置: {skill_id}")

        # 2. 檢查用戶是否已解鎖此技能
        current_skill = await global_skill_repository.get_skill_data(
            user_id, skill_id, skill_type
        )
        if not current_skill:
            raise SkillNotFoundError(f"技能尚未解鎖，無法增加經驗: {skill_id}")

        current_level = current_skill.skill_level
        current_xp = current_skill.skill_xp

        # 3. 增加經驗值
        new_xp = current_xp + xp_amount
        old_level = current_level
        new_level = current_level

        # 4. 檢查是否升級
        max_level = skill_config.get("max_level", 10)  # 默認最大等級10
        upgraded = False

        while new_level < max_level:
            required_xp = await _calculate_xp_required_for_level(
                new_level + 1, skill_id
            )
            if new_xp >= required_xp:
                new_level += 1
                new_xp -= required_xp
                upgraded = True
            else:
                break

        # 5. 更新數據庫
        if upgraded:
            await global_skill_repository.level_up_skill(
                user_id, skill_id, skill_type, new_level, new_xp
            )
        else:
            await global_skill_repository.add_skill_xp(
                user_id, skill_id, skill_type, xp_amount
            )

        logger.info(
            "用戶 %s 技能 %s 增加 %s 經驗，等級: %s -> %s",
            user_id,
            skill_id,
            xp_amount,
            old_level,
            new_level,
        )

        # 6. 返回升級結果
        if upgraded:
            return {
                "upgraded": True,
                "skill_id": skill_id,
                "skill_type": skill_type,
                "old_level": old_level,
                "new_level": new_level,
                "remaining_xp": new_xp,
                "skill_name": skill_config.get("name", skill_id),
            }
        else:
            return {
                "upgraded": False,
                "skill_id": skill_id,
                "current_level": new_level,
                "current_xp": new_xp,
                "xp_added": xp_amount,
            }

    except (ValueError, SkillNotFoundError):
        # 重新拋出業務異常
        raise
    except Exception as e:
        logger.error(
            "增加技能經驗失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
        )
        raise DatabaseOperationError(f"增加技能經驗失敗: {str(e)}") from e


async def _calculate_xp_required_for_level(
    target_level: int, skill_id: Optional[str] = None
) -> int:
    """
    計算升級到指定等級所需的經驗值（使用技能配置中的xp_to_next_level_config）

    Args:
        target_level: 目標等級
        skill_id: 技能ID（用於獲取技能特定的升級配置）

    Returns:
        所需經驗值
    """
    if skill_id:
        # 從技能配置中獲取xp_to_next_level_config
        try:
            # 使用異步方法獲取技能配置
            skill_config = await _get_skill_config(skill_id)
        except Exception:
            skill_config = {}
        xp_config = skill_config.get("xp_to_next_level_config", {})

        if xp_config:
            base_xp = xp_config.get("base_xp", 100)
            multiplier = xp_config.get("multiplier", 1.2)

            # 使用技能特定的升級公式
            required_exp = int(base_xp * (target_level**multiplier))
            return required_exp

    # 降級到通用配置
    config_loader = get_config_loader()
    try:
        skill_exp_config = await config_loader.get_skill_experience_config()
    except Exception:
        skill_exp_config = {}
    formula_config = skill_exp_config.get("skill_level_exp_formula", {})

    base_exp = formula_config.get("base_exp", 100)
    multiplier = formula_config.get("multiplier", 1.2)

    # 計算經驗值
    required_exp = int(base_exp * (target_level**multiplier))

    return required_exp


async def _get_skill_config(skill_id: str) -> Dict[str, Any]:
    """
    獲取技能配置

    Args:
        skill_id: 技能ID

    Returns:
        技能配置字典
    """
    config_loader = get_config_loader()
    # 先嘗試從active_skills獲取
    active_skills_config = await config_loader.get_config("active_skills")
    skill_config = active_skills_config.get(skill_id, {})

    if skill_config:
        return skill_config

    # 如果沒有找到，嘗試從passive_skills獲取
    passive_skills_config = await config_loader.get_config("passive_skills")
    skill_config = passive_skills_config.get(skill_id, {})

    return skill_config


async def _get_skill_rarity(skill_id: str) -> int:
    """
    獲取技能稀有度

    Args:
        skill_id: 技能ID

    Returns:
        技能稀有度（1-5）
    """
    try:
        skill_config = await _get_skill_config(skill_id)
    except Exception:
        skill_config = {}
    return skill_config.get("skill_rarity", 1)


async def get_player_learned_skills(
    user_id: int, skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None
) -> List[Dict[str, Any]]:
    """
    獲取玩家已學習的技能列表（純異常模式）

    Args:
        user_id: 玩家ID
        skill_type: 技能類型過濾（可選）

    Returns:
        技能信息列表

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 從Repository獲取技能列表
        learned_skills = await global_skill_repository.get_user_learned_skills(
            user_id, skill_type
        )

        formatted_skills = []
        for learned_skill in learned_skills:
            try:
                # 獲取技能配置
                skill_id = learned_skill.skill_id
                if not skill_id:
                    continue
                skill_config = await _get_skill_config(skill_id)

                if skill_config:
                    current_level = learned_skill.skill_level
                    current_xp = learned_skill.skill_xp
                    max_level = skill_config.get("max_level", 10)

                    # 計算升級所需經驗
                    required_xp = None
                    can_upgrade = False
                    if current_level < max_level:
                        required_xp = await _calculate_xp_required_for_level(
                            current_level + 1, skill_id
                        )
                        can_upgrade = current_xp >= required_xp

                    formatted_skills.append(
                        {
                            "skill_id": skill_id,
                            "skill_type": learned_skill.skill_type,
                            "name": skill_config.get("name", skill_id),
                            "description": skill_config.get("description_template", ""),
                            "current_level": current_level,
                            "max_level": max_level,
                            "current_xp": current_xp,
                            "xp_to_next_level": required_xp,
                            "can_upgrade": can_upgrade,
                            "unlocked_at": learned_skill.created_at,
                        }
                    )
            except Exception as skill_error:
                logger.warning(
                    "處理技能 %s 時發生錯誤: %s",
                    learned_skill.skill_id,
                    skill_error,
                )
                continue

        return formatted_skills

    except Exception as e:
        logger.error("獲取玩家技能列表時發生錯誤: %s", e, exc_info=True)
        raise DatabaseOperationError(f"獲取技能列表失敗: {str(e)}") from e


async def sacrifice_cards_universal(
    user_id: int,
    filters: "CollectionFilters",
    operation_type: str = "single",
    force_sacrifice: bool = False,
) -> Dict[str, Any]:
    """
    通用的卡牌獻祭接口（參考/sw指令設計）

    Args:
        user_id: 用戶ID
        filters: 卡牌篩選條件
        operation_type: 獻祭操作類型 ("single", "all", "keep_one")
        force_sacrifice: 是否強制獻祭最愛卡片

    Returns:
        Dict[str, Any]: 獻祭結果

    Raises:
        CardNotFoundError: 沒有找到符合條件的卡牌
        InsufficientCardsError: 沒有符合獻祭條件的卡牌
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 1. 獲取符合條件的卡牌候選者
        sacrifice_candidates = await _gather_sacrifice_candidates(user_id, filters)

        if not sacrifice_candidates:
            raise CardNotFoundError("沒有找到符合篩選條件的卡牌可供獻祭。")

        # 2. 制定獻祭計劃
        sacrifice_plan = _prepare_sacrifice_operations(
            sacrifice_candidates, operation_type, force_sacrifice
        )

        if not sacrifice_plan.get("cards_to_sacrifice"):
            raise InsufficientCardsError(
                "沒有符合獻祭條件的卡牌（可能由於數量、最愛設置或操作類型限制）。"
            )

        # 3. 執行獻祭事務
        sacrifice_result = await _execute_sacrifice_transaction(user_id, sacrifice_plan)

        return {
            "sacrificed_count": sacrifice_plan["total_cards_sacrificed"],
            "skill_exp_gained": sacrifice_plan["total_skill_exp"],
            "skills_upgraded": sacrifice_result.get("skills_upgraded", []),
            "sacrifice_details": sacrifice_plan["sacrifice_details"],
            "message": f"成功獻祭 {sacrifice_plan['total_cards_sacrificed']} 張卡牌，獲得技能經驗",
        }

    except Exception as e:
        logger.error(
            "獻祭卡牌失敗: user_id=%s, filters=%s, error=%s", user_id, filters, e
        )
        raise


async def sacrifice_cards_for_skill(
    user_id: int,
    skill_id: str,
    include_favorites: bool = False,
    keep_one: bool = False,
) -> Dict[str, Any]:
    """
    獻祭擁有指定技能的卡牌來獲得技能經驗

    Args:
        user_id: 用戶ID
        skill_id: 技能ID
        include_favorites: 是否包含最愛卡片
        keep_one: 是否保留一張

    Returns:
        Dict[str, Any]: 獻祭結果

    Raises:
        CardNotFoundError: 沒有擁有該技能的卡牌
        InsufficientCardsError: 卡牌數量不足
    """
    try:
        # 獲取擁有指定技能的卡牌
        user_cards = await player_collection_repository.get_cards_with_skill(
            user_id, skill_id
        )
        if not include_favorites:
            user_cards = [card for card in user_cards if not card.get("is_favorite")]

        if not user_cards:
            raise CardNotFoundError(f"未找到擁有技能 {skill_id} 的卡牌")

        # 根據keep_one參數確定要獻祭的卡牌
        if keep_one and len(user_cards) <= 1:
            raise InsufficientCardsError("只有一張卡牌，無法在保留一張的情況下獻祭")

        cards_to_sacrifice = user_cards[:-1] if keep_one else user_cards

        if not cards_to_sacrifice:
            raise InsufficientCardsError("沒有可獻祭的卡牌")

        # 計算獲得的技能經驗
        skill_exp_gained = await _calculate_skill_experience_from_cards(
            cards_to_sacrifice
        )

        # 執行獻祭
        sacrifice_result = await _execute_card_sacrifice(
            user_id, cards_to_sacrifice, skill_exp_gained
        )

        return {
            "sacrificed_count": len(cards_to_sacrifice),
            "skill_exp_gained": skill_exp_gained,
            "skills_upgraded": sacrifice_result.get("skills_upgraded", []),
            "total_cards_before": len(user_cards),
            "remaining_cards": len(user_cards) - len(cards_to_sacrifice),
            "target_skill_id": skill_id,
        }

    except Exception as e:
        logger.error(
            "獻祭技能卡牌失敗: user_id=%s, skill_id=%s, error=%s",
            user_id,
            skill_id,
            e,
        )
        raise


async def _gather_sacrifice_candidates(
    user_id: int, filters: CollectionFilters
) -> List[Dict[str, Any]]:
    """
    獲取符合條件的卡牌候選者（參考/sw的_gather_sell_candidates_and_stored_prices）

    Args:
        user_id: 用戶ID
        filters: 篩選條件

    Returns:
        卡牌候選者列表
    """
    logger.debug(
        f"[SACRIFICE_GATHER] User {user_id}: Gathering sacrifice candidates. Filters: {filters}"
    )

    # 獲取符合條件的卡牌快照
    # TODO: get_user_cards_with_filters has been removed.
    # This needs to be reimplemented with a different approach.
    card_snapshots = []

    if not card_snapshots:
        logger.debug("[SACRIFICE_GATHER] User %s: No snapshots found.", user_id)
        return []

    valid_candidates = []
    for snapshot in card_snapshots:
        card_id = snapshot.get("card_id")
        if card_id is None:
            logger.warning(
                f"[SACRIFICE_GATHER] User {user_id}: Snapshot missing card_id: {snapshot}"
            )
            continue

        # 計算這張卡牌的獻祭價值（技能經驗）
        sacrifice_value = await _calculate_card_sacrifice_value(snapshot)

        valid_candidates.append(
            {
                "card_id": card_id,
                "quantity": snapshot["quantity"],
                "is_favorite": snapshot.get("is_favorite", False),
                "name": snapshot.get("name", "N/A"),
                "rarity": snapshot.get("rarity"),
                "pool_type": snapshot.get("pool_type"),
                "sacrifice_value": sacrifice_value,
                "rpg_level": snapshot.get("rpg_level", 1),
                "star_level": snapshot.get("star_level", 0),
            }
        )

    logger.debug(
        f"[SACRIFICE_GATHER] User {user_id}: Found {len(valid_candidates)} valid candidates."
    )
    return valid_candidates


async def _calculate_card_sacrifice_value(card_data: Dict[str, Any]) -> Dict[str, int]:
    """
    計算單張卡牌的獻祭價值（技能經驗）
    使用技能配置中的xp_gain_on_sacrifice字段

    Args:
        card_data: 卡牌數據

    Returns:
        技能經驗字典 {skill_id: exp_amount}
    """
    skill_exp = {}

    # 獲取卡牌的所有技能（使用統一方法）
    card_skills = await _get_card_skills(card_data)

    # 為每個技能計算經驗值
    for skill_id in card_skills:
        if skill_id not in skill_exp:
            skill_exp[skill_id] = 0

        # 從技能配置中獲取xp_gain_on_sacrifice
        skill_base_exp = await _get_skill_sacrifice_exp(skill_id)

        # 根據卡牌等級和星級調整經驗值
        card_multiplier = _calculate_card_exp_multiplier(card_data)

        skill_exp[skill_id] += int(skill_base_exp * card_multiplier)

    return skill_exp


async def _get_skill_sacrifice_exp(skill_id: str) -> int:
    """
    從技能配置中獲取獻祭基礎經驗值

    Args:
        skill_id: 技能ID

    Returns:
        基礎經驗值
    """
    try:
        skill_config = await _get_skill_config(skill_id)
        return skill_config.get("xp_gain_on_sacrifice", 10)
    except Exception:
        # 默認值
        return 10


def _calculate_card_exp_multiplier(card_snapshot: Dict[str, Any]) -> float:
    """
    根據卡牌等級和星級計算經驗倍數

    Args:
        card_snapshot: 卡牌快照數據

    Returns:
        經驗倍數
    """
    multiplier = 1.0

    # 根據RPG等級增加倍數
    rpg_level = card_snapshot.get("rpg_level", 1)
    if rpg_level > 1:
        multiplier += (rpg_level - 1) * 0.1  # 每級增加10%

    # 根據星級增加倍數
    star_level = card_snapshot.get("star_level", 0)
    if star_level > 0:
        multiplier += star_level * 0.2  # 每星增加20%

    return multiplier


async def _calculate_card_base_exp(card_data: Dict[str, Any]) -> int:
    """
    統一的卡牌基礎經驗值計算方法（支持快照和對象）

    Args:
        card_data: 卡牌數據（快照字典或對象）

    Returns:
        基礎經驗值
    """
    try:
        # 獲取技能經驗配置
        config_loader = get_config_loader()
        skill_exp_config = await config_loader.get_skill_experience_config()

        # 基礎經驗值
        base_exp = skill_exp_config.get("sacrifice_base_exp", 10)

        # 統一獲取屬性的方法（支持字典和對象）
        def get_attr(data, attr_name, default=None):
            if isinstance(data, dict):
                return data.get(attr_name, default)
            else:
                return getattr(data, attr_name, default)

        # 根據卡牌等級增加經驗
        rpg_level = get_attr(card_data, "rpg_level", 1)
        if rpg_level and rpg_level > 1:
            level_multiplier = skill_exp_config.get("sacrifice_level_multiplier", 5)
            base_exp += rpg_level * level_multiplier

        # 根據星級增加經驗
        star_level = get_attr(card_data, "star_level", 0)
        if star_level:
            star_multiplier = skill_exp_config.get("sacrifice_star_multiplier", 10)
            base_exp += star_level * star_multiplier

        # 根據稀有度增加經驗
        rarity = get_attr(card_data, "rarity", 1)
        if rarity and rarity > 1:
            rarity_multiplier = skill_exp_config.get("sacrifice_rarity_multiplier", 3)
            base_exp += rarity * rarity_multiplier

        return base_exp
    except Exception as e:
        logger.warning("計算卡牌基礎經驗失敗: %s", e)
        return 10  # 返回默認值


async def _get_card_skills(card_data: Any) -> List[str]:
    """
    統一的卡牌技能獲取方法（支持快照字典和對象）

    Args:
        card_data: 卡牌數據（快照字典或對象）

    Returns:
        技能ID列表
    """
    skills = []

    # 統一獲取屬性的方法（支持字典和對象）
    def get_attr(data, attr_name, default=None):
        if isinstance(data, dict):
            return data.get(attr_name, default)
        else:
            return getattr(data, attr_name, default)

    # 獲取主動技能
    equipped_active_skills = get_attr(card_data, "equipped_active_skill_ids")
    if equipped_active_skills:
        try:
            if isinstance(equipped_active_skills, str):
                active_skills = json.loads(equipped_active_skills)
                skills.extend(active_skills)
            elif isinstance(equipped_active_skills, list):
                skills.extend(equipped_active_skills)
        except (json.JSONDecodeError, TypeError):
            pass

    # 獲取被動技能
    equipped_passives = get_attr(card_data, "equipped_common_passives")
    if equipped_passives:
        try:
            if isinstance(equipped_passives, str):
                passive_skills = json.loads(equipped_passives)
                skills.extend(passive_skills)
            elif isinstance(equipped_passives, list):
                skills.extend(equipped_passives)
        except (json.JSONDecodeError, TypeError):
            pass

    # 獲取卡牌固有技能（從配置中獲取）
    card_id = get_attr(card_data, "card_id")
    if card_id:
        try:
            # 從配置中獲取卡牌的固有技能
            config_loader = get_config_loader()
            card_config = await config_loader.get_card_config(card_id)
            if card_config:
                innate_skills = card_config.get("innate_skills", [])
                skills.extend(innate_skills)
        except Exception as e:
            logger.warning("獲取卡牌 %s 固有技能失敗: %s", card_id, e)

    return list(set(skills))  # 去重


def _prepare_sacrifice_operations(
    candidates: List[Dict[str, Any]],
    operation_type: str,
    force_sacrifice: bool,
) -> Dict[str, Any]:
    """
    制定獻祭計劃（參考/sw的_prepare_sell_operations）

    Args:
        candidates: 候選卡牌列表
        operation_type: 操作類型
        force_sacrifice: 是否強制獻祭最愛卡片

    Returns:
        獻祭計劃字典
    """
    logger.debug(
        f"[SACRIFICE_PREPARE] Preparing operations for {len(candidates)} candidates. Operation: {operation_type}, Force: {force_sacrifice}"
    )

    cards_to_sacrifice = []
    sacrifice_details = []
    total_skill_exp = {}
    total_cards_sacrificed = 0

    for candidate in candidates:
        card_id = candidate["card_id"]
        current_quantity = candidate["quantity"]
        is_favorite = candidate["is_favorite"]
        sacrifice_value = candidate["sacrifice_value"]
        card_name = candidate.get("name", "N/A")

        # 檢查是否跳過最愛卡片
        if not force_sacrifice and is_favorite:
            logger.debug(
                f"[SACRIFICE_PREPARE] Skipping card_id {card_id} (favorite and not force_sacrifice)."
            )
            continue

        # 根據操作類型確定獻祭數量
        quantity_to_sacrifice = 0
        if operation_type.lower() == "single":
            if current_quantity >= 1:
                quantity_to_sacrifice = 1
        elif operation_type.lower() == "keep_one":
            if current_quantity > 1:
                quantity_to_sacrifice = current_quantity - 1
        elif operation_type.lower() == "all":
            quantity_to_sacrifice = current_quantity
        else:
            logger.warning(
                f"[SACRIFICE_PREPARE] Unknown operation_type: {operation_type} for card {card_id}. Skipping."
            )
            continue

        if quantity_to_sacrifice > 0:
            # 計算總技能經驗
            for skill_id, exp_per_card in sacrifice_value.items():
                total_exp = exp_per_card * quantity_to_sacrifice
                if skill_id not in total_skill_exp:
                    total_skill_exp[skill_id] = 0
                total_skill_exp[skill_id] += total_exp

            # 添加到獻祭列表
            cards_to_sacrifice.append(
                {
                    "card_id": card_id,
                    "quantity": quantity_to_sacrifice,
                    "current_quantity": current_quantity,
                }
            )

            # 添加詳細信息
            sacrifice_details.append(
                {
                    "card_id": card_id,
                    "card_name": card_name,
                    "quantity_sacrificed": quantity_to_sacrifice,
                    "skill_exp_gained": {
                        k: v * quantity_to_sacrifice for k, v in sacrifice_value.items()
                    },
                }
            )

            total_cards_sacrificed += quantity_to_sacrifice

    logger.debug(
        f"[SACRIFICE_PREPARE] Plan: {len(cards_to_sacrifice)} card types to sacrifice, {total_cards_sacrificed} total cards, skill exp: {total_skill_exp}"
    )

    return {
        "cards_to_sacrifice": cards_to_sacrifice,
        "sacrifice_details": sacrifice_details,
        "total_skill_exp": total_skill_exp,
        "total_cards_sacrificed": total_cards_sacrificed,
    }


async def _execute_sacrifice_transaction(
    user_id: int, sacrifice_plan: Dict[str, Any]
) -> Dict[str, Any]:
    """
    執行獻祭事務（參考/sw的_execute_sell_in_transaction）

    Args:
        user_id: 用戶ID
        sacrifice_plan: 獻祭計劃

    Returns:
        執行結果
    """
    try:
        cards_to_sacrifice = sacrifice_plan["cards_to_sacrifice"]
        total_skill_exp = sacrifice_plan["total_skill_exp"]

        # 執行卡牌刪除操作
        for card_operation in cards_to_sacrifice:
            card_id = card_operation["card_id"]
            quantity = card_operation["quantity"]

            # 判斷卡牌類型並獻祭
            # 假設通用獻祭只處理默認卡
            await player_collection_repository.sacrifice_default_card(
                user_id, card_id, quantity
            )

        # 為技能增加經驗並檢查升級
        skills_upgraded = []
        for skill_id, exp_amount in total_skill_exp.items():
            try:
                # 調用新的技能經驗增加方法
                upgrade_result = await add_skill_experience(
                    user_id, skill_id, "ACTIVE", exp_amount
                )
                if upgrade_result and upgrade_result.get("upgraded"):
                    skills_upgraded.append(
                        {
                            "skill_id": skill_id,
                            "new_level": upgrade_result.get("new_level"),
                            "old_level": upgrade_result.get("old_level"),
                        }
                    )
            except SkillNotFoundError as e:
                logger.warning("技能 %s 尚未解鎖，無法增加經驗: %s", skill_id, e)
                # 技能未解鎖，跳過此技能
            except Exception as e:
                logger.warning("為技能 %s 增加經驗時失敗: %s", skill_id, e)
                # 繼續處理其他技能，不中斷整個流程

        logger.info(
            "用戶 %s 獻祭了 %s 張卡牌，獲得技能經驗: %s",
            user_id,
            sacrifice_plan["total_cards_sacrificed"],
            total_skill_exp,
        )

        return {
            "skills_upgraded": skills_upgraded,
            "cards_sacrificed": [op["card_id"] for op in cards_to_sacrifice],
            "total_exp_gained": sum(total_skill_exp.values()),
        }

    except Exception as e:
        logger.error("執行獻祭事務失敗: %s", e)
        raise DatabaseOperationError(f"獻祭執行失敗: {str(e)}") from e


async def _calculate_skill_experience_from_cards(cards: List[Any]) -> Dict[str, int]:
    """
    計算從卡牌獲得的技能經驗（使用統一方法）

    Args:
        cards: 卡牌列表

    Returns:
        技能經驗字典 {skill_id: exp_amount}
    """
    skill_exp = {}

    for card in cards:
        # 基礎經驗值（使用統一方法）
        base_exp = await _calculate_card_base_exp(card)

        # 獲取卡牌的所有技能（使用統一方法）
        card_skills = await _get_card_skills(card)

        # 將經驗分配給所有技能
        for skill_id in card_skills:
            if skill_id not in skill_exp:
                skill_exp[skill_id] = 0
            skill_exp[skill_id] += base_exp

    return skill_exp


async def _execute_card_sacrifice(
    user_id: int,
    cards_to_sacrifice: List[Any],
    skill_exp_gained: Dict[str, int],
) -> Dict[str, Any]:
    """
    執行卡牌獻祭（純異常模式）

    Args:
        user_id: 用戶ID
        cards_to_sacrifice: 要獻祭的卡牌列表
        skill_exp_gained: 獲得的技能經驗

    Returns:
        執行結果

    Raises:
        DatabaseOperationError: 數據庫操作失敗
    """
    try:
        # 刪除獻祭的卡牌
        for card in cards_to_sacrifice:
            card_id = card.get("card_id")
            if not card_id:
                continue

            if card.get("card_type") == "special":
                await player_collection_repository.sacrifice_special_card(card_id)
            else:
                await player_collection_repository.sacrifice_default_card(
                    user_id, card_id
                )

        # 為技能增加經驗並檢查升級
        skills_upgraded = []
        for skill_id, exp_amount in skill_exp_gained.items():
            try:
                # 調用新的技能經驗增加方法
                upgrade_result = await add_skill_experience(
                    user_id, skill_id, "ACTIVE", exp_amount
                )
                if upgrade_result and upgrade_result.get("upgraded"):
                    skills_upgraded.append(
                        {
                            "skill_id": skill_id,
                            "new_level": upgrade_result.get("new_level"),
                            "old_level": upgrade_result.get("old_level"),
                        }
                    )
            except SkillNotFoundError as e:
                logger.warning("技能 %s 尚未解鎖，無法增加經驗: %s", skill_id, e)
                # 技能未解鎖，跳過此技能
            except Exception as e:
                logger.warning("為技能 %s 增加經驗時失敗: %s", skill_id, e)
                # 繼續處理其他技能，不中斷整個流程

        logger.info(
            "用戶 %s 獻祭了 %s 張卡牌，獲得技能經驗: %s",
            user_id,
            len(cards_to_sacrifice),
            skill_exp_gained,
        )

        return {
            "skills_upgraded": skills_upgraded,
            "cards_deleted": [card.id for card in cards_to_sacrifice],
            "total_exp_gained": sum(skill_exp_gained.values()),
        }

    except Exception as e:
        logger.error("執行卡牌獻祭失敗: %s", e)
        raise DatabaseOperationError(f"獻祭執行失敗: {str(e)}") from e
