"""
Profile View - 處理檔案相關的 Discord UI 互動
"""

import io
from typing import Optional

import discord
from discord import ui

from bot import CustomAutoShardedBot
from gacha.exceptions import BusinessError
from gacha.services import profile_service
from gacha.services.ui import profile_image_generator
from utils.base_view import BaseView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed


class ProfileLikeButton(ui.Button):
    """可重用的檔案按讚按鈕組件"""

    def __init__(self, owner_id: int, row: int = 0, on_like_success_callback=None):
        """初始化按讚按鈕

        參數:
            owner_id: 檔案擁有者ID
            row: 按鈕所在行數
            on_like_success_callback: 按讚成功後的回調函數
        """
        super().__init__(
            label="❤️ 按讚",
            style=discord.ButtonStyle.primary,
            custom_id=f"profile_like_button_{owner_id}",
            row=row,
        )
        self.owner_id = owner_id
        self.on_like_success_callback = on_like_success_callback

    async def callback(self, interaction: discord.Interaction):
        """處理按讚互動"""
        # 權限檢查已移至 ProfileLikeView.interaction_check
        await interaction.response.defer()
        await profile_service.like_profile(interaction.user.id, self.owner_id)
        embed = SuccessEmbed(description="❤️ 按讚成功！")
        await interaction.followup.send(embed=embed, ephemeral=True)

        # 調用成功回調函數
        if self.on_like_success_callback:
            await self.on_like_success_callback(interaction)


class ProfileLikeView(BaseView):
    """檔案按讚與刷新視圖"""

    def __init__(
        self,
        owner_id: int,
        bot_instance: CustomAutoShardedBot,
        private_channel_id: Optional[int] = None,
    ):
        # 使用父類的 __init__，但 BaseView 的 user_id 在此不再用於權限檢查
        super().__init__(bot=bot_instance, user_id=owner_id, timeout=None)
        self.owner_id = owner_id
        self.bot: CustomAutoShardedBot = bot_instance
        self.private_channel_id = private_channel_id

        # 添加可重用的按讚按鈕
        like_button = ProfileLikeButton(owner_id, row=0)
        self.add_item(like_button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        覆寫互動檢查邏輯。
        - 按讚按鈕: 任何人都可以點擊，除了擁有者自己。
        - 刷新按鈕: 只有檔案擁有者可以點擊。
        """
        # 確保 interaction.data 存在
        if not interaction.data:
            return False

        custom_id = interaction.data.get("custom_id")

        # 處理刷新按鈕
        if custom_id == "profile_refresh_button":
            if interaction.user.id != self.owner_id:
                # 拋出異常，會自動以 ephemeral message 回覆
                raise BusinessError("只有檔案擁有者可以刷新檔案。")
            return True  # 允許互動

        # 處理按讚按鈕
        if custom_id and custom_id.startswith("profile_like_button_"):
            if interaction.user.id == self.owner_id:
                raise BusinessError("您不能為自己的檔案按讚。")
            return True  # 允許互動

        # 對於不認識的 custom_id，不允許互動
        return False

    @ui.button(
        label="🔄 刷新",
        style=discord.ButtonStyle.secondary,
        custom_id="profile_refresh_button",
        row=0,
    )
    async def refresh_profile(
        self, interaction: discord.Interaction, button: ui.Button
    ):
        """處理檔案刷新互動"""
        # 權限檢查已移至 interaction_check
        await interaction.response.defer(ephemeral=True, thinking=True)

        profile_data_obj = None
        try:
            target_user = await self.bot.fetch_user(self.owner_id)
            if not target_user:
                raise BusinessError("找不到目標用戶。")

            logger.info(
                "開始刷新用戶 %s 的個人檔案圖片 (由 %s 請求)",
                self.owner_id,
                interaction.user.id,
            )

            await profile_service.invalidate_profile_cache(self.owner_id)
            logger.info("用戶 %s 的個人檔案圖片 URL 已從數據庫清除", self.owner_id)

            user_avatar_url = (
                str(target_user.display_avatar.url)
                if target_user.display_avatar
                else None
            )
            discord_display_name = target_user.display_name
            profile_data_obj = await profile_service.get_profile_data(
                self.owner_id,
                discord_display_name=discord_display_name,
                avatar_url=user_avatar_url,
            )
            image_bytes = await profile_image_generator.generate_profile_image(
                profile_data_obj
            )

            file_for_edit = discord.File(
                io.BytesIO(image_bytes), filename="profile.png"
            )

            new_view = ProfileLikeView(
                owner_id=self.owner_id,
                bot_instance=self.bot,
                private_channel_id=self.private_channel_id,
            )

            if self.private_channel_id:
                channel = self.bot.get_channel(
                    self.private_channel_id
                ) or await self.bot.fetch_channel(self.private_channel_id)
                if channel and isinstance(channel, discord.TextChannel):
                    file_for_upload = discord.File(
                        io.BytesIO(image_bytes), filename="profile.png"
                    )
                    temp_message = await channel.send(file=file_for_upload)
                    new_image_url = temp_message.attachments[0].url
                    await profile_service.update_cached_profile_image_url(
                        self.owner_id, new_image_url
                    )
                    logger.info(
                        "刷新後的圖片已上傳至私密頻道，新URL已存儲至數據庫: %s",
                        new_image_url,
                    )
                    if interaction.message:
                        await interaction.followup.edit_message(
                            interaction.message.id,
                            content=new_image_url,
                            attachments=[],
                            view=new_view,
                        )
                else:
                    logger.warning(
                        "無法找到或訪問私密頻道 (ID: %s)，將直接發送圖片文件",
                        self.private_channel_id,
                    )
                    if interaction.message:
                        await interaction.followup.edit_message(
                            interaction.message.id,
                            content=None,
                            attachments=[file_for_edit],
                            view=new_view,
                        )
            else:
                if interaction.message:
                    await interaction.followup.edit_message(
                        interaction.message.id,
                        content=None,
                        attachments=[file_for_edit],
                        view=new_view,
                    )

            embed = SuccessEmbed(description="個人檔案已刷新！")
            await interaction.followup.send(embed=embed, ephemeral=True)
            logger.info("用戶 %s 的個人檔案已成功刷新", self.owner_id)

        finally:
            if profile_data_obj and profile_data_obj._preview_files_to_cleanup:
                await profile_service.cleanup_preview_files(
                    profile_data_obj._preview_files_to_cleanup
                )
