# auxiliary/services/ai_core/ai_service.py (重構後)

import asyncio
import base64
import json
import logging
import os
import re
import time
import uuid
from datetime import datetime
from functools import wraps
from typing import Any, Dict, Final, List, Optional, Union

from aiohttp import ClientResponseError

from auxiliary.exceptions import AIConnectionError

from . import config, http_client

logger = logging.getLogger(__name__)

# S級常數定義
MAX_PAYLOAD_SIZE: Final[int] = 50 * 1024 * 1024  # 50MB
MAX_LOG_SIZE: Final[int] = 100 * 1024 * 1024  # 100MB
NONCE_PATTERN: Final[str] = r"\[Request Nonce: [a-f0-9\-]+\]"


def performance_monitor(func):
    """性能監控裝飾器 - S級可觀測性"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.perf_counter() - start_time
            logger.info(f"{func.__name__} 執行成功: {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.perf_counter() - start_time
            logger.error(f"{func.__name__} 執行失敗: {execution_time:.3f}s, 錯誤: {e}")
            raise

    return wrapper


# --- Helper Functions ---


def _build_payload(
    messages: List[Dict[str, Any]], model: str, **kwargs
) -> Dict[str, Any]:
    """建立發送到 API 的 JSON payload。"""
    payload = {
        "model": model,
        "messages": messages,
        "temperature": kwargs.get("temperature", 1.17),
        "max_tokens": kwargs.get("max_tokens", 65536),
        "top_p": kwargs.get("top_p", 0.98),
        "presence_penalty": kwargs.get("presence_penalty", 0),
        "frequency_penalty": kwargs.get("frequency_penalty", 0),
    }
    # 移除 payload 中值為 None 的鍵
    return {k: v for k, v in payload.items() if v is not None}


async def _call_api_with_failover(
    messages: List[Dict[str, Any]],
    stream: bool = False,
    request_id: Optional[str] = None,
    **kwargs,
) -> Any:
    """
    使用我們自己的 http_client 呼叫 API，並實現故障轉移。
    【【【此為修改後的版本】】】
    """
    # ... (前面的輸入驗證部分保持不變)
    if not isinstance(messages, list):
        raise TypeError("messages 必須是列表")
    if not messages:
        raise ValueError("messages 不能為空")
    if not isinstance(stream, bool):
        raise TypeError("stream 必須是布林值")

    # 驗證 messages 結構
    for i, msg in enumerate(messages):
        if not isinstance(msg, dict):
            raise TypeError(f"messages[{i}] 必須是字典")
        if "role" not in msg or "content" not in msg:
            raise ValueError(f"messages[{i}] 必須包含 'role' 和 'content' 欄位")

    if not config.API_CONFIGS:
        raise RuntimeError("沒有可用的API配置")

    errors = {}
    sorted_configs = sorted(config.API_CONFIGS, key=lambda x: x["priority"])

    for api_config in sorted_configs:
        api_name = api_config["name"]
        api_url = api_config["endpoint"]
        api_key = api_config["api_key"]
        model = api_config["model"]

        if not all([api_url, api_key, model]):
            logger.warning(f"跳過不完整的 API 配置: {api_name}")
            continue

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        # ... (URL 組合邏輯保持不變)
        base_url = api_url.rstrip("/")
        if base_url.endswith("/v1"):
            full_url = f"{base_url}/chat/completions"
        else:
            full_url = f"{base_url}/v1/chat/completions"

        payload = _build_payload(messages, model, **kwargs)
        response = None

        try:
            safe_url = full_url.split("?")[0] if "?" in full_url else full_url
            logger.info(f"[{request_id}] 正在嘗試 API: {api_name}")
            logger.debug(f"[{request_id}] 請求 URL: {safe_url}")
            logger.debug(f"[{request_id}] 使用模型: {model}")

            response = await http_client.post_request(
                full_url, headers, payload, stream
            )

            logger.info(f"[{request_id}] API {api_name} 呼叫成功 (HTTP 200 OK)。")

            if not stream:
                try:
                    response_data = await response.json()

                    if not isinstance(response_data, dict):
                        raise ValueError(f"API {api_name} 返回非字典格式響應")

                    # =================================================================
                    # 【【【新增的核心修正：API 回應內容質檢】】】
                    # =================================================================

                    # 質檢點一：檢查 API 是否在回應內容中明確返回了 error 物件
                    if "error" in response_data:
                        error_details = response_data.get("error", {})
                        error_msg = f"API {api_name} 返回業務錯誤: {error_details.get('message', '無詳細訊息')}"
                        logger.warning(f"[{request_id}] {error_msg}")
                        errors[api_name] = error_msg
                        continue  # <-- 關鍵！觸發轉移到下一個 API

                    # 質檢點二：檢查生成中止原因 (finish_reason)
                    if (
                        response_data.get("choices")
                        and isinstance(response_data["choices"], list)
                        and len(response_data["choices"]) > 0
                    ):
                        choice = response_data["choices"][0]
                        finish_reason = choice.get("finish_reason")

                        # 定義哪些原因應該觸發轉移
                        failover_reasons = [
                            "PROHIBITED_CONTENT",
                            "CONTENT_FILTER",
                            "SAFETY",
                            "LENGTH",
                        ]

                        if finish_reason and finish_reason.upper() in failover_reasons:
                            error_msg = f"API {api_name} 因 '{finish_reason}' 中止生成。觸發 API 轉移。"
                            logger.warning(f"[{request_id}] {error_msg}")
                            errors[api_name] = error_msg
                            continue  # <-- 關鍵！觸發轉移到下一個 API

                    # 質檢點三：檢查空回應內容
                    if (
                        response_data.get("choices")
                        and isinstance(response_data["choices"], list)
                        and len(response_data["choices"]) > 0
                    ):
                        choice = response_data["choices"][0]
                        message_content = choice.get("message", {}).get("content", "")

                        # 檢查內容是否為空或只包含空白字符
                        if not message_content or not message_content.strip():
                            error_msg = (
                                f"API {api_name} 返回空回應內容。觸發 API 轉移。"
                            )
                            logger.warning(f"[{request_id}] {error_msg}")
                            errors[api_name] = error_msg
                            continue  # <-- 關鍵！觸發轉移到下一個 API
                    else:
                        # 如果沒有 choices 或 choices 為空，也視為空回應
                        error_msg = f"API {api_name} 返回無效的回應結構（缺少 choices）。觸發 API 轉移。"
                        logger.warning(f"[{request_id}] {error_msg}")
                        errors[api_name] = error_msg
                        continue  # <-- 關鍵！觸發轉移到下一個 API

                    # =================================================================
                    # 【【【修正結束】】】
                    # =================================================================

                    # 如果通過所有質檢，這才是一個真正的成功回應
                    logger.info(f"[{request_id}] API {api_name} 質檢通過，返回結果。")
                    # 始終保存API響應日誌，不受日誌級別限制
                    asyncio.create_task(
                        _save_api_response_to_json(
                            response_data, messages, request_id, api_name
                        )
                    )
                    return response_data  # <-- 只有真正成功才返回

                except (ValueError, TypeError, asyncio.TimeoutError) as json_error:
                    logger.error(
                        f"[{request_id}] API {api_name} JSON 解析失敗: {json_error}"
                    )
                    raise ValueError(f"API {api_name} 響應格式無效") from json_error
            else:
                # 流式回應直接返回，讓調用者處理
                return response

        except (ClientResponseError, asyncio.TimeoutError) as e:
            error_msg = f"API {api_name} 網路錯誤: {type(e).__name__}: {e}"
            logger.warning(f"[{request_id}] {error_msg}")
            errors[api_name] = error_msg
            continue  # 繼續嘗試下一個 API
        except Exception as e:
            error_msg = f"API {api_name} 未知錯誤: {type(e).__name__}: {e}"
            logger.error(f"[{request_id}] {error_msg}", exc_info=True)
            errors[api_name] = error_msg
            continue  # 繼續嘗試下一個 API

    # 如果所有 API 都失敗
    logger.error(f"[{request_id}] 所有 AI API 調用均失敗。錯誤詳情: {errors}")
    raise AIConnectionError("AI 服務暫時不可用，所有備用 API 均嘗試失敗。")


# --- Public-Facing Functions (給 Cogs 使用的介面) ---


def _prepare_messages(
    system_prompt: str,
    prompt: Optional[str] = None,
    history: Optional[List[Dict[str, Any]]] = None,
    image_data_b64: Optional[str] = None,
    nonce: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """統一的訊息準備函數。"""
    messages: List[Dict[str, Any]] = [{"role": "system", "content": system_prompt}]

    if history:
        messages.extend(history)

    current_nonce = nonce or str(uuid.uuid4())
    prompt_with_nonce = f"{prompt or ''}\\n\\n[Request Nonce: {current_nonce}]"

    user_content: Union[str, List[Dict[str, Any]]]
    if image_data_b64:
        user_content = [
            {"type": "text", "text": prompt_with_nonce},
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_data_b64}"},
            },
        ]
    else:
        user_content = prompt_with_nonce

    messages.append({"role": "user", "content": user_content})
    return messages


async def process_messages(
    messages: List[Dict[str, Any]], request_id: Optional[str] = None, **kwargs
) -> Dict[str, Any]:
    """直接處理一個已經構建好的 messages 列表。"""
    response_data = await _call_api_with_failover(
        messages, stream=False, request_id=request_id, **kwargs
    )
    return response_data


async def process_text(
    system_prompt: str,
    prompt: str,
    history: Optional[List[Dict[str, str]]] = None,
    request_id: Optional[str] = None,
    nonce: Optional[str] = None,
    **kwargs,
) -> Dict[str, Any]:
    """處理純文字請求。"""
    messages = _prepare_messages(
        system_prompt=system_prompt, prompt=prompt, history=history, nonce=nonce
    )
    response_data = await _call_api_with_failover(
        messages, stream=False, request_id=request_id, **kwargs
    )
    return response_data


async def process_with_image(
    image_data: bytes,
    prompt: str,
    system_prompt: str,
    request_id: Optional[str] = None,
    nonce: Optional[str] = None,
    **kwargs,
) -> Dict[str, Any]:
    """處理帶有圖片的請求。"""
    # 輸入驗證 - 性能關鍵路徑優化
    if not isinstance(image_data, bytes):
        raise TypeError("image_data 必須是 bytes 類型")
    if not image_data:
        raise ValueError("image_data 不能為空")
    if len(image_data) > 100 * 1024 * 1024:  # 100MB 限制
        raise ValueError(f"圖片過大: {len(image_data)} bytes")

    try:
        # 圖像壓縮邏輯保持不變
        resized_image_bytes = await resize_image(image_data)

        if not isinstance(prompt, str):
            logger.warning(
                "process_with_image 接收到的 prompt 不是字符串類型，而是 %s。"
                "將嘗試轉換。",
                type(prompt),
            )
            try:
                prompt = str(prompt)
            except Exception as e:
                raise TypeError(
                    "Prompt must be a string or convertible to a string."
                ) from e

        b64_image = base64.b64encode(resized_image_bytes).decode("utf-8")
        messages = _prepare_messages(
            system_prompt=system_prompt,
            prompt=prompt,
            image_data_b64=b64_image,
            nonce=nonce,
        )
        response_data = await _call_api_with_failover(
            messages, stream=False, request_id=request_id, **kwargs
        )
        return response_data
    except ValueError:
        raise
    except Exception as e:
        logger.error("處理圖像請求時發生未知錯誤: %s", str(e), exc_info=True)
        raise


def _get_raw_content_from_response(api_response: Dict[str, Any]) -> Optional[str]:
    """從 API 回應字典中安全地提取原始 content 字串。"""
    try:
        # 新的字典格式優先
        if "choices" in api_response and api_response["choices"]:
            content = api_response["choices"][0]["message"]["content"]
            if isinstance(content, str) and content.strip():
                return content
        logger.warning("在 API 回應中找不到有效的 content。")
        return None
    except (KeyError, IndexError, TypeError):
        logger.warning("解析 API 回應 content 時發生錯誤。", exc_info=True)
        return None


def extract_response_text(
    api_response: Dict[str, Any], require_ai_payload: bool = True
) -> str:
    """
    從已經是字典格式的 API 回應中提取文本。
    """
    # 使用通用的 parser 函數
    raw_text = _get_raw_content_from_response(api_response)

    if not raw_text:
        logger.error("無效的API響應格式: %s", api_response)
        return "無法獲取回答，API響應格式無效。"

    try:
        if raw_text:
            # 如果不需要 ai_payload 標籤檢查，直接返回原始內容
            if not require_ai_payload:
                return raw_text.strip()

            # 檢查 ai_payload 標籤
            payload_match = re.search(
                r"<ai_payload>(.*?)</ai_payload>", raw_text, re.DOTALL
            )
            if payload_match:
                return payload_match.group(1).strip()

            loose_match = re.search(
                r"<ai[_\\-\\s]*payload[^>]*>(.*?)</ai[_\\-\\s]*payload[^>]*>",
                raw_text,
                re.DOTALL | re.IGNORECASE,
            )
            if loose_match:
                extracted_text = loose_match.group(1).strip()
                logger.warning(
                    "使用寬鬆匹配提取到近似的 ai_payload 內容: %s...",
                    extracted_text[:100],
                )
                return extracted_text

            logger.warning(
                "在AI回應中未找到任何 <ai_payload> 標籤，將返回處理後的原始內容"
            )
            cleaned_text = re.sub(r"\\*思考[:：].*?\\*", "", raw_text, flags=re.DOTALL)
            cleaned_text = re.sub(
                r"\\*[Tt]hinking[:：].*?\\*", "", cleaned_text, flags=re.DOTALL
            )
            cleaned_text = re.sub(
                r"步驟\\s*\\d+\\s*[:：].*?(?=步驟\\s*\\d+\\s*[:：]|$)",
                "",
                cleaned_text,
                flags=re.DOTALL,
            )
            cleaned_text = re.sub(
                r"^.*?(我將|我會|我應該|I will|I should|I need to).*?(?=\\n|$)",
                "",
                cleaned_text,
                flags=re.MULTILINE,
            )
            cleaned_text = re.sub(r"\\n{3,}", "\\n\\n", cleaned_text)
            cleaned_text = cleaned_text.strip()

            if cleaned_text:
                return cleaned_text

            logger.error(
                "在AI回應中未找到可用內容。原始回應片段: %s...", raw_text[:200]
            )
            return f"AI回應未遵循格式要求。AI嘗試說: {raw_text[:200]}..."
        else:
            logger.error("API響應中沒有content字段")
            return "無法獲取回答，API響應格式異常。"
    except (KeyError, IndexError) as e:
        logger.error(f"從 API 回應中提取文本時出錯: {e}")
        return "解析 AI 回應時發生錯誤。"


# --- Service Management Functions ---


def initialize_ai_service():
    """
    初始化AI服務模組
    """
    if not config.API_CONFIGS:
        logger.critical("AI服務初始化失敗：未找到任何API配置")
        raise RuntimeError("AI服務初始化失敗：未找到任何API配置")

    logger.info("使用API配置列表進行初始化，共 %d 個API", len(config.API_CONFIGS))
    logger.info(f"AI服務初始化完成，配置了 {len(config.API_CONFIGS)} 個API端點")

    # 設定日誌級別
    logging.getLogger("aiohttp").setLevel(logging.ERROR)
    logging.getLogger("aiohttp.access").setLevel(logging.ERROR)


async def close_ai_service():
    """
    關閉AI服務模組，清理資源
    """
    await http_client.close_session()
    logger.info("AI 服務已關閉")


# --- 圖像處理函數 ---


async def resize_image(
    image_data: bytes,
    max_size: Optional[int] = None,
    max_dimensions: Optional[tuple] = None,
) -> bytes:
    def _sync_resize_image():
        from io import BytesIO

        from PIL import Image, UnidentifiedImageError

        if not image_data or not isinstance(image_data, bytes):
            raise ValueError("無效的圖像數據或數據為空。")

        nonlocal max_size, max_dimensions
        if max_size is None:
            max_size = config.MAX_IMAGE_SIZE
        if max_dimensions is None:
            max_dimensions = config.MAX_IMAGE_DIMENSIONS

        try:
            with BytesIO(image_data) as input_buffer, Image.open(input_buffer) as img:
                # 圖像格式轉換和尺寸調整
                if img.mode in ("RGBA", "P"):
                    img = img.convert("RGB")

                if max_dimensions:
                    img.thumbnail(max_dimensions, Image.Resampling.LANCZOS)

                # 圖像壓縮
                output_buffer = BytesIO()
                quality = 95
                img.save(output_buffer, format="JPEG", quality=quality, optimize=True)

                while output_buffer.tell() > max_size and quality > 30:
                    output_buffer.seek(0)
                    output_buffer.truncate()
                    quality -= 5
                    img.save(
                        output_buffer, format="JPEG", quality=quality, optimize=True
                    )

                return output_buffer.getvalue()

        except UnidentifiedImageError as e:
            raise ValueError(
                "文件不是有效的圖像格式。請上傳 JPEG、PNG、GIF、BMP、WebP 或 TIFF 等"
                "受支援的格式。"
            ) from e
        except Exception as e:
            logger.error("處理圖像時發生未知錯誤: %s", e, exc_info=True)
            raise ValueError(f"處理圖像時出錯: {e}") from e

    return await asyncio.to_thread(_sync_resize_image)


def simple_base64_encode(image_data: bytes) -> str:
    return base64.b64encode(image_data).decode("utf-8")


# --- 日誌相關函數保持不變 ---


def _get_log_source(
    messages: List[Dict[str, Any]],
) -> str:
    """從訊息中提取日誌來源"""
    if messages and len(messages) > 0:
        first_msg = messages[0]
        if isinstance(first_msg, dict) and "content" in first_msg:
            system_prompt = first_msg["content"]
            if isinstance(system_prompt, str):
                if "穿搭" in system_prompt or "outfit" in system_prompt.lower():
                    return "outfit"
                if "問答" in system_prompt or "qa" in system_prompt.lower():
                    return "qa"
    return "unknown"


def _clean_log_messages(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """簡化的日誌清理函數 - 高併發優化"""

    def _process_content(content: Any) -> str:
        """Process message content for logging"""
        if isinstance(content, list):
            return f"[複合消息包含 {len(content)} 個元素]"
        return str(content)

    def _clean_sensitive_info(content: str) -> str:
        """Remove sensitive information from content"""
        return re.sub(r"\[Request Nonce: [a-f0-9\-]+\]", "[Nonce:***]", content)

    # 使用列表推導式更簡潔
    return [
        {
            "role": msg.get("role", "unknown"),
            "content": _clean_sensitive_info(_process_content(msg.get("content", ""))),
        }
        for msg in messages
    ]


def _prepare_log_data(
    api_response,
    messages: List[Dict[str, Any]],
    request_id: Optional[str],
    api_type_used: str,
) -> Dict[str, Any]:
    """準備要寫入日誌的資料"""
    timestamp = int(datetime.now().timestamp())
    req_id = request_id or "no_id"
    source = _get_log_source(messages)
    cleaned_messages = _clean_log_messages(messages)

    # 找到對應的 API 配置資訊
    api_config = None
    for config_item in config.API_CONFIGS:
        if config_item["name"] == api_type_used:
            api_config = config_item
            break

    return {
        "timestamp": timestamp,
        "formatted_time": datetime.fromtimestamp(timestamp).strftime(
            "%Y-%m-%d %H:%M:%S"
        ),
        "request_id": req_id,
        "source": source,
        "api_type_used": api_type_used,
        "api_config": {
            "url": api_config["endpoint"] if api_config else "unknown",
            "model": api_config["model"] if api_config else "unknown",
            "key_configured": bool(api_config["api_key"]) if api_config else False,
        },
        "request": {"messages": cleaned_messages},
        "response": api_response,
    }


async def _save_api_response_to_json(
    api_response: Dict[str, Any],
    messages: List[Dict[str, Any]],
    request_id: Optional[str] = None,
    api_type_used: Optional[str] = "unknown",
) -> None:
    """保存API響應到JSON文件 (非阻塞版本)"""

    def _sync_save_to_json():
        try:
            # 調試模式：不限制日誌大小，方便問題排查
            max_log_size = 100 * 1024 * 1024  # 100MB，實際上不限制

            data_to_save = _prepare_log_data(
                api_response, messages, request_id, api_type_used or "unknown"
            )

            # 檢查數據大小
            data_str = json.dumps(data_to_save, ensure_ascii=False)
            if len(data_str.encode("utf-8")) > max_log_size:
                logger.warning(
                    "API響應日誌過大 (%d bytes)，截斷保存",
                    len(data_str.encode("utf-8")),
                )
                # 截斷響應內容
                if "response" in data_to_save and isinstance(
                    data_to_save["response"], dict
                ):
                    data_to_save["response"] = {
                        "truncated": True,
                        "original_size": len(data_str.encode("utf-8")),
                        "choices": data_to_save["response"].get("choices", [])[
                            :1
                        ],  # 只保留第一個選擇
                    }

            source = data_to_save["source"]
            req_id = data_to_save["request_id"]
            timestamp = data_to_save["timestamp"]

            log_dir = os.path.join("logs", "api_responses")
            date_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d")
            source_dir = os.path.join(log_dir, date_str, source)
            os.makedirs(source_dir, exist_ok=True)

            filename = f"{timestamp}_{req_id}_{source}.json"
            filepath = os.path.join(source_dir, filename)

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error("保存API響應到JSON時出錯: %s", str(e), exc_info=True)

    try:
        await asyncio.to_thread(_sync_save_to_json)
    except Exception as e:
        logger.error("背景保存API響應時出錯: %s", str(e), exc_info=True)
