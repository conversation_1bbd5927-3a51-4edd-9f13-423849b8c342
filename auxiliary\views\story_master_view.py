# story_master_view.py

from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union  # NEW: Import List

import discord
from discord.ext import commands
from discord.ui import TextInput

from auxiliary.exceptions import AIConnectionError, AuxiliaryError
from auxiliary.services import story_logic
from auxiliary.services.story.constants import (
    EMBED_DESCRIPTION_LIMIT,
    MIN_FLOORS_FOR_SUMMARY,
    UIComponentID,
)
from auxiliary.services.story.story_logic import THEMES_BY_TITLE
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from utils.base_modal import BaseModal
from utils.response_embeds import SuccessEmbed, WarningEmbed

if TYPE_CHECKING:
    from auxiliary.cogs.story_cog import StoryCog

# 常數已移至 auxiliary.services.story.constants


# NEW: 新增一個輔助函數來智能分割文本
def split_text_nicely(text: str, max_length: int) -> List[str]:
    """
    將長文本分割成多個部分，盡量在自然的斷句點（如換行符）分割。
    """
    if len(text) <= max_length:
        return [text]

    parts = []
    while len(text) > max_length:
        # 找到在長度限制內最後一個換行符
        split_pos = text.rfind("\n", 0, max_length)
        # 如果找不到換行符，就硬性分割
        if split_pos == -1:
            split_pos = max_length

        parts.append(text[:split_pos])
        text = text[split_pos:].lstrip()  # 移除下一段開頭的空白

    parts.append(text)
    return parts


class CustomResponseModal(BaseModal, title="自訂你的回應"):
    # ... (此部分無需修改)
    response = TextInput(
        label="你的決定是...",
        style=discord.TextStyle.long,
        placeholder="輸入你的行動或對話...",
        required=True,
    )

    def __init__(self, story_view: "StoryMasterView"):
        super().__init__(bot=story_view.bot, title="自訂你的回應")
        self.story_view = story_view

    async def on_submit(self, interaction: discord.Interaction):
        await self.story_view.process_choice(interaction, self.response.value)


class StorySelectMenu(discord.ui.Select):
    # ... (此部分無需修改)
    def __init__(self, story_view: "StoryMasterView"):
        self.story_view = story_view

        turn_index = (story_view.current_page - 1) * 2
        ai_turn = story_view.turns_cache.get(turn_index)
        options_data = ai_turn.get("options", []) if ai_turn else []

        select_options = []
        for i, option_text in enumerate(options_data):
            if not option_text:
                continue
            force_split_threshold = 20
            desc_max_len = 100
            option_label = ""
            option_description = None
            if len(option_text) > force_split_threshold:
                split_point = option_text.rfind("，", 0, force_split_threshold)
                if split_point != -1:
                    option_label = option_text[: split_point + 1]
                    option_description = option_text[split_point + 1 :].strip()
                else:
                    option_label = option_text[: force_split_threshold - 3] + "..."
                    option_description = option_text
            else:
                option_label = option_text
            if option_description and len(option_description) > desc_max_len:
                option_description = option_description[: desc_max_len - 3] + "..."
            select_options.append(
                discord.SelectOption(
                    label=option_label,
                    description=option_description,
                    value=str(i),
                )
            )
        if not select_options:
            select_options.append(
                discord.SelectOption(label="暫無可用選項", value="no_op", default=True)
            )
        super().__init__(
            placeholder="請選擇你的下一步行動...",
            min_values=1,
            max_values=1,
            options=select_options,
            row=1,
            custom_id=UIComponentID.STORY_SELECT_MENU,
        )

    async def callback(self, interaction: discord.Interaction):
        selected_index_str = self.values[0]
        if selected_index_str == "no_op":
            await interaction.response.defer()
            return

        # 確保只有在最新頁面才能進行選擇
        if self.story_view.current_page != self.story_view.total_pages:
            await interaction.response.send_message(
                "只能在最新進度頁面進行選擇，請先回到最新頁面。", ephemeral=True
            )
            return

        # The view's state is always in sync because components are rebuilt on each update.
        # The try/except block is not necessary here.
        selected_index = int(selected_index_str)
        turn_index = (self.story_view.current_page - 1) * 2
        ai_turn = self.story_view.turns_cache.get(turn_index)
        if not ai_turn:
            # This should not happen if the view is properly updated
            await interaction.response.send_message(
                "發生錯誤，找不到對應的劇情選項，請稍後再試。", ephemeral=True
            )
            return

        options_data = ai_turn.get("options", [])
        if selected_index >= len(options_data):
            await interaction.response.send_message(
                "選項索引超出範圍，請重新選擇。", ephemeral=True
            )
            return

        choice = options_data[selected_index]
        await self.story_view.process_choice(interaction, choice)


class StoryMasterView(BasePaginationView):
    def __init__(
        self,
        bot: Union[commands.Bot, commands.AutoShardedBot],
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
        history_data: Dict[str, Any],
    ):
        self.story_cog = story_cog
        self.story_meta = history_data["story"]
        self.story_id = self.story_meta["id"]
        self.theme_title = self.story_meta["theme_title"]

        # --- PERFORMANCE REFACTOR: 按需加載 ---
        self.total_turn_count = history_data["total_turn_count"]
        self.turns_cache: Dict[int, Dict[str, Any]] = {}  # 緩存已加載的回合

        total_pages = (self.total_turn_count + 1) // 2
        if total_pages == 0 and self.total_turn_count == 1:  # 處理只有開幕詞的新故事
            total_pages = 1

        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=total_pages,
            total_pages=total_pages,
            timeout=None,
        )

        # --- NEW: 新增子分頁狀態 ---
        self.current_sub_page = 1
        self.total_sub_pages = 1
        # --- END NEW ---


        # 按鈕將在 start() 或 _update_page() 中動態添加


    async def start(self) -> discord.Embed:
        """
        Generates the initial embed for the view.
        This should be called by the cog to get the first message payload.
        """
        # --- PERFORMANCE REFACTOR: 按需加載 ---
        # 第一次需要手動加載最後一頁的數據
        await self._ensure_page_data_loaded(self.current_page)
        # --- END REFACTOR ---

        embed = await self._create_story_embed()

        # Refresh buttons to match the initial state
        await self._add_buttons_for_page(self.current_page)
        self._add_sub_pagination_buttons()
        self._refresh_button_states()

        return embed

    async def _handle_generation_failure(
        self, interaction: discord.Interaction, choice: str
    ):
        # ... (此部分無需修改)
        error_embed = discord.Embed(
            title="❌ 劇情生成失敗",
            description=(
                "AI 未能成功生成後續劇情，這可能是暫時的網絡問題或內部錯誤。\n\n"
                "您可以點擊下方按鈕重試。"
            ),
            color=discord.Color.red(),
        )
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = True
        existing_regenerate_button = next(
            (
                item
                for item in self.children
                if isinstance(item, discord.ui.Button)
                and item.custom_id == UIComponentID.REGENERATE_STORY
            ),
            None,
        )
        if existing_regenerate_button:
            self.remove_item(existing_regenerate_button)

        async def retry_callback(interaction: discord.Interaction):
            await self.process_choice(interaction, choice)

        retry_button = discord.ui.Button(
            label="🔄 重新生成",
            style=discord.ButtonStyle.danger,
            custom_id=UIComponentID.RETRY_GENERATION,
            row=4,
        )
        retry_button.callback = retry_callback
        if not any(
            isinstance(item, discord.ui.Button)
            and item.custom_id == UIComponentID.RETRY_GENERATION
            for item in self.children
        ):
            self.add_item(retry_button)
        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=error_embed, view=self)
        else:
            await interaction.edit_original_response(embed=error_embed, view=self)

    async def _update_view_post_generation(self, interaction: discord.Interaction):
        # --- MODIFIED: 在更新視圖時重置子分頁 ---
        self.current_sub_page = 1
        # --- END MODIFIED ---

        # --- PERFORMANCE REFACTOR: 只獲取摘要信息 ---
        summary_data = await story_logic.get_user_history(self.user_id)
        if summary_data:
            self.story_meta = summary_data["story"]
            self.story_id = self.story_meta["id"]
            self.theme_title = self.story_meta["theme_title"]
            self.total_turn_count = summary_data["total_turn_count"]
            self.total_pages = (self.total_turn_count + 1) // 2
            self.current_page = self.total_pages
            self.turns_cache.clear()  # 清除舊的緩存
            await self._update_page(self.total_pages, interaction)
        else:
            # ... (此部分無需修改)
            error_embed = discord.Embed(
                title="❌ 錯誤",
                description="無法獲取更新後的故事數據，可能需要重新開始。",
                color=discord.Color.red(),
            )
            clear_button = discord.ui.Button(
                label="清除故事紀錄", style=discord.ButtonStyle.danger
            )

            async def clear_callback(interaction: discord.Interaction):
                await story_logic.clear_history(interaction.user.id)
                embed = SuccessEmbed(
                    description="故事紀錄已清除，請使用 `/story start` 重新開始。"
                )
                await interaction.response.edit_message(embed=embed, view=None)

            clear_button.callback = clear_callback
            view = discord.ui.View()
            view.add_item(clear_button)
            await interaction.edit_original_response(embed=error_embed, view=view)

    async def _add_buttons_for_page(self, page: int):
        self._clear_story_buttons()

        turn_index = (page - 1) * 2
        ai_turn = self.turns_cache.get(turn_index)

        # 如果沒有回合數據（例如，在數據加載前），則不添加任何按鈕
        if not ai_turn:
            return

        is_latest_page = page == self.total_pages

        if is_latest_page:
            # 只有在真正的最新頁面才添加互動選項
            options = ai_turn.get("options")
            if options and page == self.total_pages:  # 雙重確認是最新頁面
                self.add_item(StorySelectMenu(self))
            if page == self.total_pages:  # 確保只在最新頁面顯示
                custom_button = discord.ui.Button(
                    label="自訂回應...",
                    style=discord.ButtonStyle.secondary,
                    custom_id=UIComponentID.CUSTOM_RESPONSE,
                    row=2,
                )
                custom_button.callback = self.custom_response_callback
                self.add_item(custom_button)
            if self.total_turn_count > 1:
                regenerate_button = discord.ui.Button(
                    label="🔄 重新生成",
                    style=discord.ButtonStyle.danger,
                    custom_id=UIComponentID.REGENERATE_STORY,
                    row=2,
                )
                regenerate_button.callback = self.regenerate_callback
                self.add_item(regenerate_button)
                share_button = discord.ui.Button(
                    label="🔗 分享故事",
                    style=discord.ButtonStyle.secondary,
                    custom_id=UIComponentID.SHARE_STORY,
                    row=2,
                )
                share_button.callback = self.share_story_callback
                self.add_item(share_button)

                # NEW: 新增總結管理按鈕
                if (
                    self.total_turn_count > MIN_FLOORS_FOR_SUMMARY
                ):  # 只有超過指定樓層數才顯示總結管理
                    summary_button = discord.ui.Button(
                        label="🗂️ 總結管理",
                        style=discord.ButtonStyle.secondary,
                        custom_id="summary_management",
                        row=3,
                    )
                    summary_button.callback = self.summary_management_callback
                    self.add_item(summary_button)
        else:
            revert_button = discord.ui.Button(
                label="🌀 從此處繼續",
                style=discord.ButtonStyle.success,
                custom_id=UIComponentID.REVERT_AND_CONTINUE,
                row=4,
            )
            revert_button.callback = self.revert_callback
            self.add_item(revert_button)

        # --- NEW: 新增呼叫，添加內容子分頁按鈕 ---
        self._add_sub_pagination_buttons()
        # --- END NEW ---

    def _clear_story_buttons(self):
        """
        全面清理所有非基礎分頁的UI元件。
        """
        # 獲取基礎分頁按鈕的 custom_id 列表（通常由 BasePaginationView 定義）
        base_pagination_ids = {"first_page", "prev_page", "next_page", "last_page", "jump"}
        
        components_to_remove = [
            item
            for item in self.children
            if getattr(item, "custom_id", None) not in base_pagination_ids
        ]
        
        for component in components_to_remove:
            self.remove_item(component)

    # --- NEW: 新增管理子分頁按鈕的方法 ---
    def _add_sub_pagination_buttons(self):
        # 先清除舊的子分頁按鈕
        components_to_remove = [
            item
            for item in self.children
            if getattr(item, "custom_id", None)
            in [UIComponentID.SUB_PREV, UIComponentID.SUB_NEXT]
        ]
        for comp in components_to_remove:
            self.remove_item(comp)

        # 如果總共只有一頁或少於一頁，則不顯示按鈕
        if self.total_sub_pages <= 1:
            return

        prev_button = discord.ui.Button(
            label="◀️ 上一頁內容",
            style=discord.ButtonStyle.secondary,
            custom_id=UIComponentID.SUB_PREV,
            row=3,
            disabled=(self.current_sub_page == 1),
        )
        prev_button.callback = self.sub_page_previous_callback
        self.add_item(prev_button)

        next_button = discord.ui.Button(
            label="下一頁內容 ▶️",
            style=discord.ButtonStyle.secondary,
            custom_id=UIComponentID.SUB_NEXT,
            row=3,
            disabled=(self.current_sub_page == self.total_sub_pages),
        )
        next_button.callback = self.sub_page_next_callback
        self.add_item(next_button)

    async def sub_page_previous_callback(self, interaction: discord.Interaction):
        if self.current_sub_page > 1:
            self.current_sub_page -= 1
        await self._update_page(self.current_page, interaction)

    async def sub_page_next_callback(self, interaction: discord.Interaction):
        if self.current_sub_page < self.total_sub_pages:
            self.current_sub_page += 1
        await self._update_page(self.current_page, interaction)

    # --- END NEW ---

    # ... (其他 callback 方法如 custom_response_callback, share_story_callback 等無需修改) ...
    async def custom_response_callback(self, interaction: discord.Interaction):
        # 確保只有在最新頁面才能進行自訂回應
        if self.current_page != self.total_pages:
            await interaction.response.send_message(
                "只能在最新進度頁面進行自訂回應。請先回到最新頁面。", ephemeral=True
            )
            return
        await interaction.response.send_modal(CustomResponseModal(self))

    async def share_story_callback(self, interaction: discord.Interaction):
        is_public = await story_logic.toggle_story_public(
            interaction.user.id, self.story_id
        )
        if is_public:
            embed = SuccessEmbed(
                title="🔗 故事分享",
                description=f"你的故事已設為公開！\n\n**分享ID：** `{self.story_id}`\n\n其他人可以使用指令 `/story view story_id:{self.story_id}` 來查看你的故事。\n再次點擊此按鈕可以取消分享。",
            )
        else:
            embed = WarningEmbed(
                title="🔒 取消分享",
                description="你的故事已設為私人，其他人無法再查看。",
            )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    async def revert_callback(self, interaction: discord.Interaction):
        user_id = interaction.user.id
        if user_id in self.story_cog.active_story_users:
            raise AuxiliaryError("你目前有另一個故事操作正在進行中，請稍候再試。")
        self.story_cog.active_story_users.add(user_id)
        try:
            await interaction.response.defer()
            for item in self.children:
                if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                    item.disabled = True
            await interaction.edit_original_response(
                embed=discord.Embed(
                    title="<a:Loading:1392930453219967149> 正在回溯時光...",
                    description=f"準備回到第 {self.current_page} 頁的劇情節點...",
                    color=discord.Color.blue(),
                ),
                view=self,
            )
            ai_turn_index = (self.current_page - 1) * 2
            target_turn = self.turns_cache.get(ai_turn_index)
            if target_turn:
                target_turn_number = target_turn["turn_number"]
                await story_logic.revert_story_to_turn(
                    self.user_id, self.story_id, target_turn_number
                )
                await self._update_view_post_generation(interaction)
            else:
                # This case should be rare if data is loaded correctly
                raise AuxiliaryError("錯誤：找不到要回溯的劇情點，請先導航到該頁面。")
        finally:
            self.story_cog.active_story_users.discard(user_id)

    async def regenerate_callback(self, interaction: discord.Interaction):
        user_id = interaction.user.id
        if user_id in self.story_cog.active_story_users:
            raise AuxiliaryError("你目前有另一個故事操作正在進行中，請稍候再試。")
        self.story_cog.active_story_users.add(user_id)
        try:
            await interaction.response.defer()
            for item in self.children:
                if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                    item.disabled = True
            await interaction.edit_original_response(
                embed=discord.Embed(
                    title="<a:Loading:1392930453219967149> 正在重新生成劇情...",
                    description="請稍候，正在思考新的可能性...",
                    color=discord.Color.orange(),
                ),
                view=self,
            )
            try:
                result = await story_logic.regenerate_last_turn(
                    self.user_id, self.story_id, interaction.user.display_name
                )
                if result:
                    await self._update_view_post_generation(interaction)
                else:
                    await self._handle_generation_failure(interaction, "（重新生成）")
            except AIConnectionError:
                await self._handle_generation_failure(interaction, "（重新生成）")
        finally:
            self.story_cog.active_story_users.discard(user_id)

    async def _create_story_embed(self) -> discord.Embed:
        """根據當前狀態建立並返回故事 Embed，使用智慧分頁佈局。"""

        page = self.current_page
        is_latest_page = page == self.total_pages
        turn_index = (page - 1) * 2

        # --- PERFORMANCE REFACTOR: 從緩存讀取 ---
        ai_turn = self.turns_cache.get(turn_index)
        user_turn = self.turns_cache.get(turn_index + 1)

        if not ai_turn:
            # 如果數據尚未加載，顯示一個加載中的提示
            return discord.Embed(
                title="<a:Loading:1392930453219967149> 正在加載劇情...",
                description=f"正在獲取第 {page} 頁的內容...",
                color=discord.Color.light_grey(),
            )
        # --- END REFACTOR ---

        # === 新增：智慧分頁邏輯 ===
        full_content = ai_turn.get("content", "")
        status_block_content = ai_turn.get("status_block", "")

        # 根據整體佈局規劃決定分頁策略
        pages_data = self._plan_content_layout(
            full_content, status_block_content, user_turn, self.current_sub_page
        )

        # 更新分頁狀態
        self.total_sub_pages = len(pages_data)
        if self.current_sub_page > self.total_sub_pages:
            self.current_sub_page = 1

        # 獲取當前子頁面的數據
        current_page_data = (
            pages_data[self.current_sub_page - 1]
            if pages_data
            else {"description": "", "field": None}
        )

        # 建立 Embed
        story_title = self.story_meta.get("title", self.theme_title)
        embed = discord.Embed(
            title=f"📖 {story_title} - {'最新進度' if is_latest_page else '歷史回顧'}",
            description=current_page_data["description"],
            color=(
                discord.Color.green()
                if is_latest_page
                else discord.Color.from_rgb(114, 137, 218)
            ),
        )

        # 如果當前頁面有 Field，添加它
        if current_page_data["field"]:
            embed.add_field(
                name="📊 角色狀態 📊", value=current_page_data["field"], inline=False
            )

        theme_data = THEMES_BY_TITLE.get(self.theme_title)
        if theme_data and theme_data.get("image_url"):
            embed.set_thumbnail(url=theme_data["image_url"])

        # 更新頁腳以顯示子分頁狀態
        footer_text = ""
        if is_latest_page:
            footer_text = "📍 這是最新進度 | 請選擇你的行動"
        else:
            footer_text = (
                f"第 {page}/{self.total_pages} 頁 | 點擊下方按鈕可從此處覆蓋原劇情"
            )

        if self.total_sub_pages > 1:
            footer_text += f" (內容 {self.current_sub_page}/{self.total_sub_pages})"

        embed.set_footer(text=footer_text)
        return embed

    def _plan_content_layout(
        self,
        main_content: str,
        status_content: str,
        user_turn: Dict[str, Any] | None,
        current_sub_page: int,
    ) -> List[Dict[str, Any]]:
        """
        智慧佈局規劃：根據內容長度決定最佳的分頁策略
        返回格式：[{'description': str, 'field': Optional[str]}, ...]
        """
        # 準備用戶選擇的內容
        user_choice_text = ""
        if user_turn:
            user_choice_text = f"\n\n**🤔 你的選擇**\n`{user_turn['content']}`"

        pages = []

        # 情況判斷：status_content 是否可以放入 Field
        if not status_content or len(status_content) <= 1024:
            # 情況 A：status_content 可以放入 Field（或不存在）
            # 只需要分割主要內容
            base_description = f"**📖 故事情節**\n{main_content}{user_choice_text}"
            split_descriptions = split_text_nicely(
                base_description, EMBED_DESCRIPTION_LIMIT
            )

            # status_content 只在最後一個子分頁顯示
            for i, desc in enumerate(split_descriptions):
                is_last_sub_page = i == len(split_descriptions) - 1
                field_content = (
                    status_content if (status_content and is_last_sub_page) else None
                )

                pages.append({"description": desc, "field": field_content})

        else:
            # 情況 B：status_content 太長，必須與主要內容合併
            # 組合所有內容
            combined_content = f"**📖 故事情節**\n{main_content}{user_choice_text}\n\n📊 角色狀態 📊\n{status_content}"
            split_combined = split_text_nicely(
                combined_content, EMBED_DESCRIPTION_LIMIT
            )

            # 每個分頁都沒有 Field
            for combined_part in split_combined:
                pages.append({"description": combined_part, "field": None})

        return (
            pages
            if pages
            else [{"description": "**📖 故事情節**\n（暫無內容）", "field": None}]
        )

    async def _ensure_page_data_loaded(self, page: int):
        """確保指定頁面的回合數據已從資料庫加載到緩存中。"""
        turn_index = (page - 1) * 2
        # 只需要檢查第一個回合是否存在即可判斷該頁是否已加載
        if turn_index not in self.turns_cache:
            # 從資料庫異步獲取數據
            turns_data = await story_logic.get_story_turns_for_page(self.story_id, page)
            # 填充緩存
            for i, turn in enumerate(turns_data):
                self.turns_cache[turn_index + i] = turn

    # --- MAJOR MODIFICATION: 核心修改 _update_page ---
    async def _update_page(self, page: int, interaction: discord.Interaction):
        if not interaction.response.is_done():
            await interaction.response.defer()

        # 當主回合頁碼改變時，重置子內容頁碼為 1
        if self.current_page != page:
            self.current_page = page
            self.current_sub_page = 1

        # --- PERFORMANCE REFACTOR: 按需加載 ---
        await self._ensure_page_data_loaded(page)
        # --- END REFACTOR ---

        embed = await self._create_story_embed()

        await self._add_buttons_for_page(page)
        self._refresh_button_states()

        await interaction.edit_original_response(embed=embed, view=self)

    # --- END MAJOR MODIFICATION ---

    async def process_choice(self, interaction: discord.Interaction, choice: str):
        # 確保只有在最新頁面才能進行選擇
        if self.current_page != self.total_pages:
            await interaction.response.send_message(
                "只能在最新進度頁面進行選擇。請點擊翻頁按鈕回到最新頁面。",
                ephemeral=True,
            )
            return

        user_id = interaction.user.id
        if user_id in self.story_cog.active_story_users:
            raise AuxiliaryError("你目前有另一個故事操作正在進行中，請稍候再試。")
        self.story_cog.active_story_users.add(user_id)
        try:
            for item in self.children:
                if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                    item.disabled = True
            if not interaction.response.is_done():
                await interaction.response.defer()
            await interaction.edit_original_response(
                embed=discord.Embed(
                    title="<a:Loading:1392930453219967149> 正在發展劇情中...",
                    description=f"你的選擇是：「{choice}」",
                    color=discord.Color.light_grey(),
                ),
                view=self,
            )
            try:
                result = await story_logic.continue_story(
                    self.user_id, interaction.user.display_name, self.story_id, choice
                )
                if result:
                    await self._update_view_post_generation(interaction)
                else:
                    await self._handle_generation_failure(interaction, choice)
            except AIConnectionError:
                await self._handle_generation_failure(interaction, choice)
        finally:
            self.story_cog.active_story_users.discard(user_id)

    async def summary_management_callback(self, interaction: discord.Interaction):
        """切換到總結管理模式的回調函數。"""
        from auxiliary.views.summary_management_view import SummaryManagementView
        
        # 創建回調函數而非直接引用，避免循環引用
        async def back_to_story_callback(back_interaction: discord.Interaction):
            await self._update_page(self.current_page, back_interaction)
        
        # 創建獨立的總結管理視圖
        summary_view = SummaryManagementView(
            bot=self.bot,
            user=interaction.user,
            story_cog=self.story_cog,
            story_id=self.story_id,
            story_meta=self.story_meta,
            total_turn_count=self.total_turn_count,
            back_callback=back_to_story_callback,  # 使用回調函數而非直接引用
        )
        
        # 獲取初始嵌入並更新消息
        embed = await summary_view.start()
        await interaction.response.edit_message(embed=embed, view=summary_view)
