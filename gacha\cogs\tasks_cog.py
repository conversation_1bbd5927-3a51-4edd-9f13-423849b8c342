from discord.ext import commands, tasks

from database.postgresql.async_manager import get_pool
from utils.logger import logger


class TasksCog(commands.Cog):
    """一個專門用於管理資料庫定期刷新任務的 Cog。"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.refresh_stats_materialized_views.start()
        logger.info("TasksCog 已載入，統計物化視圖刷新任務已啟動。")

    async def cog_unload(self):
        self.refresh_stats_materialized_views.cancel()
        logger.info("TasksCog 已卸載，統計物化視圖刷新任務已停止。")

    @tasks.loop(hours=1)  # 每 1 小時執行一次
    async def refresh_stats_materialized_views(self):
        logger.info("[TASKS] 開始刷新統計相關的物化視圖...")
        try:
            pool = get_pool()
            async with pool.acquire() as conn:
                # 使用 CONCURRENTLY 進行並行刷新，不影響用戶查詢
                await conn.execute(
                    "REFRESH MATERIALIZED VIEW CONCURRENTLY gacha_user_rankings_mv;"
                )
                logger.info("[TASKS] 已成功刷新 gacha_user_rankings_mv。")

                await conn.execute(
                    "REFRESH MATERIALIZED VIEW CONCURRENTLY gacha_server_stats_mv;"
                )
                logger.info("[TASKS] 已成功刷新 gacha_server_stats_mv。")

                await conn.execute(
                    "REFRESH MATERIALIZED VIEW CONCURRENTLY gacha_user_game_rankings_mv;"
                )
                logger.info("[TASKS] 已成功刷新 gacha_user_game_rankings_mv。")

                await conn.execute(
                    "REFRESH MATERIALIZED VIEW CONCURRENTLY gacha_user_luck_summary_mv;"
                )
                logger.info("[TASKS] 已成功刷新 gacha_user_luck_summary_mv。")

                logger.info("[TASKS] 統計相關物化視圖刷新完成。")
        except Exception as e:
            logger.error("[TASKS] 刷新統計物化視圖時發生錯誤: %s", e, exc_info=True)

    @refresh_stats_materialized_views.before_loop
    async def before_refresh(self):
        await self.bot.wait_until_ready()
        logger.info("[TASKS] 機器人已上線，統計刷新任務即將開始循環。")


async def setup(bot: commands.Bot):
    await bot.add_cog(TasksCog(bot))
