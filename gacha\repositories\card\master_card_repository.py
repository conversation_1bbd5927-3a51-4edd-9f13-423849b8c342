"""
卡片主表存儲庫模組 - 統一管理 gacha_master_cards 表的模組級函數
"""

from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple, Union, cast

import asyncpg

from gacha.constants import RarityLevel
from gacha.exceptions import CardNotFoundError
from gacha.models.models import Card
from gacha.repositories._base_repo import (
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)
from utils.logger import logger

# 表名常量
TABLE_NAME = "gacha_master_cards"


async def get_cards_by_criteria(
    rarity: Optional[RarityLevel] = None,
    pool_types: Optional[List[str]] = None,
    card_ids: Optional[List[int]] = None,
    series: Optional[str] = None,
    search_query: Optional[str] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    random_order: bool = False,
    return_ids_only: bool = False,
    connection: Optional[asyncpg.Connection] = None,
) -> Union[List[Card], List[int]]:
    """
    統一的卡片查詢方法，根據條件獲取卡片

    Args:
        rarity: 卡片稀有度
        pool_types: 卡池類型列表
        card_ids: 指定的卡片ID列表
        series: 卡片系列
        search_query: 卡片名稱搜索關鍵字
        limit: 最大返回數量
        offset: 開始偏移
        random_order: 是否隨機排序
        return_ids_only: 是否只返回ID而非完整卡片物件
        connection: 可選的數據庫連接

    Returns:
        根據return_ids_only返回卡片ID列表或卡片物件列表
    """
    # 直接從數據庫查詢
    return await _query_cards_from_db(
        rarity,
        pool_types,
        card_ids,
        series,
        search_query,
        limit,
        offset,
        random_order,
        return_ids_only,
        connection,
    )


def _build_where_clauses(
    rarity: Optional[RarityLevel],
    pool_types: Optional[List[str]],
    card_ids: Optional[List[int]],
    series: Optional[str],
    search_query: Optional[str],
) -> Tuple[List[str], List[Any], int]:
    """構建 WHERE 子句和參數"""
    params: List[Any] = []
    param_idx = 1
    where_clauses = []

    if rarity is not None:
        where_clauses.append(f"rarity = ${param_idx}")
        params.append(rarity.value)
        param_idx += 1
    if pool_types:
        where_clauses.append(f"pool_type = ANY(${param_idx}::text[])")
        params.append(pool_types)
        param_idx += 1
    if card_ids:
        where_clauses.append(f"card_id = ANY(${param_idx}::integer[])")
        params.append(card_ids)
        param_idx += 1
    if series:
        where_clauses.append(f"series = ${param_idx}")
        params.append(series)
        param_idx += 1
    if search_query:
        where_clauses.append(f"name ILIKE ${param_idx}")
        params.append(f"%{search_query}%")
        param_idx += 1

    return where_clauses, params, param_idx


def _build_pagination_clause(
    limit: Optional[int], offset: Optional[int], params: List[Any], param_idx: int
) -> Tuple[str, List[Any]]:
    """構建分頁子句"""
    pagination = ""
    if limit is not None:
        pagination += f" LIMIT ${param_idx}"
        params.append(limit)
        param_idx += 1
        if offset is not None:
            pagination += f" OFFSET ${param_idx}"
            params.append(offset)
            param_idx += 1
    return pagination, params


def _process_card_results(results: List[asyncpg.Record]) -> List[Card]:
    """處理卡片查詢結果"""
    cards = []
    for res in results:
        if res:
            try:
                cards.append(Card.from_db_record(cast(Dict[str, Any], res)))
            except ValueError as e:
                logger.error(
                    "[MasterCardRepository._query_cards_from_db] Error mapping card data: %s",
                    e,
                    exc_info=True,
                )
                continue
    return cards


async def _query_cards_from_db(
    rarity: Optional[RarityLevel],
    pool_types: Optional[List[str]],
    card_ids: Optional[List[int]],
    series: Optional[str],
    search_query: Optional[str],
    limit: Optional[int],
    offset: Optional[int],
    random_order: bool,
    return_ids_only: bool,
    connection: Optional[asyncpg.Connection],
) -> Union[List[Card], List[int]]:
    """從數據庫查詢卡片"""
    # 構建 WHERE 子句
    where_clauses, params, param_idx = _build_where_clauses(
        rarity, pool_types, card_ids, series, search_query
    )
    where_condition = " AND ".join(where_clauses)

    # 構建排序子句
    if random_order:
        order_clause = "ORDER BY RANDOM()"  # 真正的隨機排序
    else:
        # 對於抽卡系統，使用固定排序利用索引，在內存中隨機化更高效
        order_clause = "ORDER BY card_id"

    # 構建分頁子句
    pagination, params = _build_pagination_clause(limit, offset, params, param_idx)

    # 構建查詢
    columns = "card_id" if return_ids_only else "*"
    query = f"SELECT {columns} FROM {TABLE_NAME}"
    if where_condition:
        query += f" WHERE {where_condition}"
    query += f" {order_clause} {pagination}"

    # 執行查詢
    results = await fetch_all(query, params, connection=connection)

    if return_ids_only:
        return [res["card_id"] for res in results if res]
    else:
        return _process_card_results(results)


async def get_card(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Card:
    """獲取單張卡片信息，如果不存在則拋出異常"""
    cards = await get_cards_by_criteria(
        card_ids=[card_id], connection=connection, return_ids_only=False
    )
    if not cards or not isinstance(cards[0], Card):
        raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)
    return cards[0]


async def get_card_optional(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[Card]:
    """獲取單張卡片信息，如果不存在則返回None"""
    try:
        return await get_card(card_id, connection=connection)
    except CardNotFoundError:
        return None


async def get_cards_details_by_ids(
    card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> List[Card]:
    """根據卡片ID列表批量獲取卡片詳細信息"""
    if not card_ids:
        return []
    cards = await get_cards_by_criteria(
        card_ids=card_ids, connection=connection, return_ids_only=False
    )
    return [card for card in cards if isinstance(card, Card)]


async def get_card_objects_by_rarity(
    rarity: RarityLevel,
    pool_types: Optional[List[str]] = None,
    limit: Optional[int] = None,
) -> List[Card]:
    """根據稀有度和卡池類型隨機獲取指定數量的卡片對象列表"""
    cards = await get_cards_by_criteria(
        rarity=rarity,
        pool_types=pool_types,
        limit=limit,
        random_order=True,
        return_ids_only=False,
    )
    return [card for card in cards if isinstance(card, Card)]


async def get_all_series() -> List[str]:
    """获取所有系列"""
    query = f"SELECT DISTINCT series FROM {TABLE_NAME} ORDER BY series"
    results = await fetch_all(query)
    return [result["series"] for result in results]


async def get_series_card_count(series: str) -> int:
    """获取指定系列的卡片数量"""
    query = f"SELECT COUNT(*)::integer as count FROM {TABLE_NAME} WHERE series = $1"
    count = await fetch_value(query, (series,))
    return count or 0


async def get_total_cards_count(connection: Optional[asyncpg.Connection] = None) -> int:
    """獲取主表中的卡片總數"""
    query = f"SELECT COUNT(*)::integer as count FROM {TABLE_NAME}"
    count = await fetch_value(query, connection=connection)
    return count or 0


async def get_all_cards_by_rarity(
    rarity: RarityLevel, pool_types: Optional[List[str]] = None
) -> List[int]:
    """
    獲取指定稀有度的所有卡片 ID

    🚀 性能優化：使用固定排序從數據庫獲取，在內存中隨機化
    這樣可以利用索引提升查詢性能，同時保證隨機性

    Args:
        rarity: 卡片稀有度
        pool_types: 卡池類型列表

    Returns:
        卡片ID列表（在 DrawEngineService 中會進行隨機化）
    """
    card_ids = await get_cards_by_criteria(
        rarity=rarity,
        pool_types=pool_types,
        return_ids_only=True,
        random_order=False,  # 🚀 使用固定排序，利用索引性能
    )
    return [cid for cid in card_ids if isinstance(cid, int)]


async def get_card_details_for_pricing(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """根據卡片ID獲取用於價格計算的特定卡片詳細信息，如果不存在則拋出異常"""
    query = f"""
        SELECT
            card_id,
            sell_price,
            pool_type,
            rarity
        FROM {TABLE_NAME}
        WHERE card_id = $1
    """
    result = await fetch_one(query, (card_id,), connection=connection)
    if not result:
        logger.warning(
            "Card with ID %s not found in get_card_details_for_pricing.", card_id
        )
        raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)

    return {
        "card_id": result["card_id"],
        "sell_price": result["sell_price"],
        "pool_type": result["pool_type"],
        "rarity": RarityLevel(result["rarity"]),
    }


async def get_paginated_cards_by_criteria(
    pool_type: Optional[str],
    rarity: Optional[RarityLevel],
    search_query: Optional[str] = None,
    specific_card_id: Optional[int] = None,
    page: int = 1,
    per_page: int = 1,
    connection: Optional[asyncpg.Connection] = None,
) -> Tuple[List[Card], int]:
    """根據多個條件獲取卡片分頁列表"""
    if page < 1:
        page = 1
    if per_page < 1:
        per_page = 1
    offset = (page - 1) * per_page
    pool_types = [pool_type] if pool_type else None
    card_ids = [specific_card_id] if specific_card_id else None
    count_params = []
    count_param_idx = 1
    count_where_clauses = []
    if rarity is not None:
        count_where_clauses.append(f"rarity = ${count_param_idx}")
        count_params.append(rarity.value)
        count_param_idx += 1
    if pool_types:
        count_where_clauses.append(f"pool_type = ANY(${count_param_idx}::text[])")
        count_params.append(pool_types)
        count_param_idx += 1
    if card_ids:
        count_where_clauses.append(f"card_id = ANY(${count_param_idx}::integer[])")
        count_params.append(card_ids)
        count_param_idx += 1
    if search_query:
        count_where_clauses.append(f"name ILIKE ${count_param_idx}")
        count_params.append(f"%{search_query}%")
        count_param_idx += 1
    count_where_condition = (
        " AND ".join(count_where_clauses) if count_where_clauses else ""
    )
    count_query = f"SELECT COUNT(*) FROM {TABLE_NAME}"
    if count_where_condition:
        count_query += f" WHERE {count_where_condition}"
    total_count = await fetch_value(count_query, count_params, connection=connection)
    if not total_count or total_count == 0:
        return ([], 0)
    cards_result = await get_cards_by_criteria(
        rarity=rarity,
        pool_types=pool_types,
        card_ids=card_ids,
        search_query=search_query,
        limit=per_page,
        offset=offset,
        connection=connection,
        return_ids_only=False,
    )
    cards = [card for card in cards_result if isinstance(card, Card)]
    return (cards, total_count or 0)


async def get_card_page_number(
    target_card_id: int,
    pool_type: Optional[str],
    rarity: Optional[RarityLevel],
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[int]:
    """獲取卡片在分頁中的頁碼"""
    conditions: List[str] = []
    params: List[Any] = []
    param_counter = 1
    if pool_type:
        conditions.append(f"pool_type = ${param_counter}")
        params.append(pool_type)
        param_counter += 1
    if rarity:
        conditions.append(f"rarity = ${param_counter}")
        params.append(rarity.value)
        param_counter += 1
    where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ""
    query = f"\n            SELECT row_num\n            FROM (\n                SELECT card_id, ROW_NUMBER() OVER (ORDER BY card_id ASC) - 1 as row_num\n                FROM {TABLE_NAME}\n                {where_clause}\n            ) AS filtered_cards\n            WHERE card_id = ${param_counter};\n        "
    params.append(target_card_id)
    row_index = await fetch_value(query, params, connection=connection)
    if row_index is not None:
        return row_index + 1
    return None


async def get_first_matching_card_id_by_name(
    name_query: str,
    pool_type: Optional[str],
    rarity: Optional[RarityLevel],
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[int]:
    """根據名稱獲取第一個匹配的卡片ID"""
    pool_types = [pool_type] if pool_type else None
    card_ids_result = await get_cards_by_criteria(
        rarity=rarity,
        pool_types=pool_types,
        search_query=name_query,
        limit=1,
        return_ids_only=True,
        connection=connection,
    )
    card_ids = [cid for cid in card_ids_result if isinstance(cid, int)]
    return card_ids[0] if card_ids else None


async def get_card_with_stored_price(
    card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """獲取卡片名稱和預存的市場價格，如果不存在則拋出異常"""
    query = "SELECT card_id, name, current_market_sell_price FROM gacha_master_cards WHERE card_id = $1"
    row = await fetch_one(query, (card_id,), connection=connection)
    if not row:
        raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)
    return {
        "card_id": row["card_id"],
        "name": row["name"],
        "current_market_sell_price": row["current_market_sell_price"],
    }


async def get_cards_details_for_pricing_batch(
    card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Dict[int, Dict[str, Any]]:
    """批量獲取卡片定價詳情"""
    if not card_ids:
        return {}
    query = f"\n            SELECT\n                card_id,\n                sell_price,\n                pool_type,\n                rarity\n            FROM {TABLE_NAME}\n            WHERE card_id = ANY($1::integer[])\n        "
    results_map: Dict[int, Dict[str, Any]] = {}
    records = await fetch_all(query, (card_ids,), connection=connection)
    if records:
        for record_data in records:
            fetched_card_id = record_data["card_id"]
            results_map[fetched_card_id] = {
                "card_id": fetched_card_id,
                "sell_price": record_data["sell_price"],
                "pool_type": record_data["pool_type"],
                "rarity": RarityLevel(record_data["rarity"]),
            }
    if len(results_map) != len(set(card_ids)):
        found_ids = set(results_map.keys())
        missing_ids = [cid for cid in set(card_ids) if cid not in found_ids]
        logger.warning(
            "Cards with IDs %s not found in get_cards_details_for_pricing_batch.",
            missing_ids,
        )
    return results_map


async def bulk_update_market_prices(
    price_updates: Dict[int, Decimal], connection: Optional[asyncpg.Connection] = None
) -> int:
    """批量更新卡片市場價格"""
    if not price_updates:
        return 0
    card_ids = list(price_updates.keys())
    new_prices_for_db = [price_updates[cid] for cid in card_ids]
    current_time = datetime.now(timezone.utc)
    query = f"\n            WITH updates_data (card_id, new_price) AS (\n                SELECT * FROM unnest($1::integer[], $2::decimal[])\n            )\n            UPDATE {TABLE_NAME} AS mc\n            SET\n                current_market_sell_price = ud.new_price,\n                last_price_update_at = $3\n            FROM updates_data ud\n            WHERE mc.card_id = ud.card_id;\n        "
    status = await execute_query(
        query, (card_ids, new_prices_for_db, current_time), connection=connection
    )

    return status


async def get_all_master_card_ids() -> List[int]:
    """獲取所有活躍卡片的ID列表"""
    card_ids = await get_cards_by_criteria(return_ids_only=True)
    return [cid for cid in card_ids if isinstance(cid, int)]


async def bulk_update_favorite_counts(
    favorite_updates: List[Dict[str, Any]],
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """批量更新卡片收藏計數（帶死鎖防護）"""
    if not favorite_updates:
        return 0

    # 【解決死鎖的關鍵】按 card_id 排序以確保所有事務以相同順序鎖定行
    sorted_updates = sorted(favorite_updates, key=lambda x: x["card_id"])
    card_ids = [update["card_id"] for update in sorted_updates]
    deltas = [update["delta"] for update in sorted_updates]

    target_table_name = "gacha_card_market_stats"
    query = f"""
            UPDATE {target_table_name} AS ms
            SET favorite_count = COALESCE(ms.favorite_count, 0) + data.delta
            FROM (
                SELECT UNNEST($1::integer[]) AS card_id, UNNEST($2::integer[]) AS delta
            ) AS data
            WHERE ms.card_id = data.card_id;
        """
    status = await execute_query(query, (card_ids, deltas), connection=connection)
    return status
