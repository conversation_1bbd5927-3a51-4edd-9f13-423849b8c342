# config.yaml - Gacha 系統配置文件
# 請注意：敏感信息 (如 API 金鑰、數據庫密碼) 應通過 .env 文件設置，它們會覆蓋此處的對應值。

discord:
  # 用於上傳和快取圖片的私密頻道 ID (設定為 null 將禁用快取功能)
  private_channel_id: 1337887637729579051 # 請設定一個私密頻道 ID，例如: 1234567890123456789

database:
  pool_min_size: 10   # 調整最小連接數
  pool_max_size: 100  # 設置為100個連接，匹配PostgreSQL最大連接數

gacha_stock_integration:
  tasks: # 各定時任務的執行頻率 - 統一改為24小時以優化性能
    calculate_supply_demand_modifier_hours: 24  # 從1小時改為24小時
    update_stock_prices_minutes: 10  # 股票價格更新保持10分鐘（股票系統需要）
    # generate_ai_news_hours: 2 # 此設定已移至 news_scheduler_manager_minutes
    news_scheduler_manager_minutes: 5  # 新聞調度保持5分鐘（股票系統需要）
    calculate_daily_anchor_prices_hours: 24
    calculate_gacha_category_stock_influence_minutes: 1440  # 從1分鐘改為1440分鐘（24小時）
    cleanup_expired_news_effects_minutes: 5  # 清理過期新聞效果保持5分鐘
    full_price_recalculation_hours: 24 # 定期完整價格校驗的執行頻率 (小時)
    check_stock_lifecycle_minutes: 15 # 檢查股票生命週期狀態的任務頻率（分鐘）

  task_initial_delays: # 各任務的啟動延遲時間（秒）
    update_stock_prices_seconds: 300  # 股價更新延遲5分鐘啟動，避免反覆重啟時瘋狂更新

  supply_demand: # 供需模型相關參數
    weights:
      supply: 0.5
      owner_diversity_effect_on_supply: 0.2
      wish: 0.3
      fav: 0.2
    smooth_factor: 0.1 # 供需因子平滑係數
    modifier_defaults: # 卡片供需修正因子邊界值的默認值
      min: 0.1
      max: 10.0

  stock_market: # 股市模型相關參數
    global_market_volatility_factor: 0.7  # 全局市場波動因子 (影響所有股票的波動幅度)
    default_base_volatility: 0.01   # 新股票創建時的默認基礎波動率 (例如 0.01 代表 +/-1% 的標準差)
    default_volatility_factor: 1.5 # 新股票創建時的默認波動放大因子
    news_impact: # 新聞直接影響股價的百分比範圍 (乘以波動放大因子 volatility_factor 後)
      relevance_window_hours: 1 # 新聞在此時間窗口內有效 (小時)
      # 例如，正面新聞導致股價至少上漲 0.3% * volatility_factor
      positive_min: 0.003
      # 例如，正面新聞導致股價最多上漲 1% * volatility_factor
      positive_max: 0.01
      # 例如，負面新聞導致股價最多下跌 1% * volatility_factor
      negative_min: -0.01
      # 例如，負面新聞導致股價至少下跌 0.3% * volatility_factor
      negative_max: -0.003
      neutral: 0.0
    max_price_change_percent: 0.05  # 單次更新最大價格變動百分比 (+/- 5%)
    global_max_asset_price: 200000  # 全域股票最高價格
    temp_news_effect: # 新聞對 Gacha 類別的臨時影響 (直接的修正值偏移量)
      # 例如，正面新聞使關聯 Gacha 類別影響因子臨時 +1%
      offset_positive: 0.01
      # 例如，負面新聞使關聯 Gacha 類別影響因子臨時 -1%
      offset_negative: -0.01
      # 臨時效果最短持續時間 (小時)
      duration_hours_min: 2
      # 臨時效果最長持續時間 (小時)
      duration_hours_max: 5
    # 股價變動百分比映射到 Gacha 類別基礎影響偏移量的因子
    price_change_to_modifier_offset_factor: 0.5
    category_influence_clamp: # Gacha 類別股票基礎影響因子的最終鉗制範圍
      min: 0.5
      max: 2.0
    strong_news_impact_threshold: 0.035 # 強新聞影響閾值，絕對值大於此值的新聞被視為"強"新聞
    news_type_generation_intervals_minutes: # 各新聞類型生成間隔 (分鐘)
      PTT_ANALYSIS: 240
      CITIZEN_STORY: 360
      INSIDER_TIP: 720
      ANALYST_REPORT: 300
      CORPORATE_ANNOUNCEMENT: 480
      REGULATORY_CHANGE: 600
      MARKET_RUMOR: 180
      TECHNICAL_SIGNAL: 210
      GENERAL_MARKET_NEWS: 150
    min_active_companies: 7 # 市場上應維持的最低活躍公司數量
    st_trigger_low_price_threshold: 1.5
  transaction_fee_rate: 0.015 # 股票交易手續費率 (1.5%)
  minimum_transaction_fee: 5.0 # 最低股票交易手續費 (油幣)

market_stats_updater: # 市場統計更新服務 (BatchedMarketStatsUpdaterService) 配置
  batch_size: 500       # 事件緩衝區大小達到此值時觸發批量處理
  batch_interval_seconds: 1.0 # 定期處理事件緩衝區的間隔（秒）

price_update_service: # 價格更新服務 (PriceUpdateService) 配置
  queue_max_size: 10000     # 異步隊列的最大大小
  batch_size: 100           # 批處理的大小
  batch_interval_seconds: 5.0 # 批處理的時間間隔（秒）
  # worker_count: 1 # 如果未來需要多個 worker，可以取消註釋並實現

highest_star_maintenance_service: # 最高星級維護服務配置
  queue_max_size: 5000      # 隊列大小警告閾值（不再限制隊列大小，避免任務丟失）
  batch_size: 50            # 批處理的大小
  batch_interval_seconds: 3.0 # 批處理的時間間隔（秒）

ai_assistant:
  # API 配置列表 - 按優先級順序排列，系統會依序嘗試直到成功
  apis:
    - priority: 3
      name: "本地API"
      endpoint: "http://127.0.0.1:8050/v1"
      model: "[EXPRESS] gemini-2.5-pro"
      api_key: "26015792"

    - priority: 2
      name: "第三API"
      endpoint: "http://127.0.0.1:8888/v1"
      model: "gemini-2.5-pro-preview-06-05"
      api_key: "123456"

    - priority: 1
      name: "備用API"
      endpoint: "http://147.93.184.134:3005/v1"
      model: "gemini-2.5-pro"
      api_key: "sk-yOon94YUGQzU43gFrL30"





# cache:
  # default_ttl_seconds: 300 # Redis 緩存默認 TTL (秒) (示例)

# Gacha 核心玩法參數 (如果未來決定從 gacha/config.py 遷移)
# gacha_core:
#   pool_costs:
#     main: 30
#     special: 120
#     summer: 90