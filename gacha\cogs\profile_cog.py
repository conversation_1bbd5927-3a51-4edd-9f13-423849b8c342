"""
Profile COG - 處理用戶檔案相關的 Discord 指令
"""

from typing import Optional, Union

import discord
from discord import app_commands
from discord.ext import commands

from bot import CustomAutoShardedBot  # 導入自定義 Bot 類型
from config.app_config import get_settings
from gacha.exceptions import (
    BusinessError,
    ProfileBackgroundError,
    ProfileUpdateError,
)
from gacha.services import profile_service
from gacha.views.profile.profile_settings_view import ProfileSettingsView
from gacha.views.profile.profile_view import ProfileLikeView
from utils.logger import logger
from utils.response_embeds import SuccessEmbed

# from utils.playwright_manager import PlaywrightManager # No longer used here


class ProfileCog(commands.Cog):
    """處理用戶檔案相關指令的 Cog"""

    def __init__(self, bot: CustomAutoShardedBot):  # <-- 修正類型提示
        self.bot = bot
        # 立即設置 private_channel_id，避免在 cog_load 中出現 AttributeError
        self.private_channel_id = get_settings().discord.private_channel_id

        # 不再需要預熱 - 每次使用新頁面

        # 記錄 private_channel_id 狀態
        if self.private_channel_id:
            logger.info("從配置檔案中讀取私密頻道ID: %s", self.private_channel_id)
        else:
            logger.warning("未在配置中找到 private_channel_id，某些功能可能受限。")

        logger.info("ProfileCog 已成功加載並初始化。")

    async def cog_load(self):
        """COG 載入時的初始化"""
        if self.private_channel_id:
            # 現在類型提示正確，可以直接賦值
            self.bot.private_channel_id = self.private_channel_id
            logger.info(
                f"已將 private_channel_id ({self.private_channel_id}) 設定到 bot 物件。"
            )

    async def _handle_set_main_card(self, user_id: int, card_id: int):
        """处理设置主展示卡片的逻辑，异常直接向上传递"""
        # 异常将由调用方（如 Modal 的 on_submit）处理
        return await profile_service.set_main_card(user_id, card_id)

    async def _handle_set_sub_cards(self, user_id: int, sub_cards_str: str):
        """处理设置副展示卡片的逻辑，异常直接向上传递"""
        results = []
        card_id_strs = [c.strip() for c in sub_cards_str.split(",") if c.strip()]

        if not (1 <= len(card_id_strs) <= 4):
            raise BusinessError("請提供1到4個有效的副卡片ID，用逗號分隔。")

        for i, card_id_str_item in enumerate(card_id_strs):
            slot_to_set = i + 1
            try:
                card_id_int_item = int(card_id_str_item)
                result = await profile_service.set_sub_card(
                    user_id, slot_to_set, card_id_int_item
                )
                results.append(result)
            except ValueError as e:
                # 重新抛出带有更多上下文的 ValueError
                raise BusinessError(
                    f"設定副卡片 (ID: {card_id_str_item}) 失敗: 卡片ID必須是有效的數字。"
                ) from e
            # 其他业务异常 (UserDoesNotOwnCardError, ProfileCardSetError, etc.) 将直接向上传递

        return results

    async def _handle_clear_sub_card_slot(self, user_id: int, slot_to_clear: int):
        """处理清除副卡片槽位的逻辑，异常直接向上传递"""
        return await profile_service.clear_sub_card(user_id, slot_to_clear)

    async def _handle_reset_background(self, user_id: int):
        """处理重置背景的逻辑，异常直接向上传递"""
        await profile_service.reset_background(user_id)
        return None

    async def _handle_set_background_image_to_local_storage(
        self,
        user_id: int,
        image_attachment: discord.Attachment,
        interaction: discord.Interaction,
    ):
        """
        直接從用戶上傳的附件下載背景圖片到本地存儲，异常直接向上传递
        """
        if (
            not image_attachment.content_type
            or not image_attachment.content_type.startswith("image/")
        ):
            raise ProfileBackgroundError("請上傳有效的圖片文件 (PNG, JPG, GIF 等)。")

        # 文件大小限制
        if image_attachment.size > 8 * 1024 * 1024:
            raise ProfileBackgroundError("圖片文件過大，請上傳小於 8MB 的圖片。")

        # 讀取圖片數據
        image_bytes = await image_attachment.read()

        # 記錄上傳信息
        logger.info(
            f"用戶背景圖片上傳 - "
            f"用戶: {interaction.user} ({user_id}), "
            f"文件名: {image_attachment.filename}, "
            f"大小: {image_attachment.size} bytes, "
            f"類型: {image_attachment.content_type}"
        )

        # 使用 ProfileService 的新方法直接處理圖片數據
        await profile_service.set_background_image_from_bytes(
            user_id, image_bytes, image_attachment.filename or "background.png"
        )

        logger.info("用戶 %s 的背景圖片已成功保存到本地存儲", user_id)
        return True

    async def _handle_set_status(self, user_id: int, status_text: str):
        """处理设置个性签名的逻辑，异常直接向上传递"""
        if len(status_text) > 150:
            raise BusinessError("個性簽名不能超過150個字符。")

        return await profile_service.set_user_status(user_id, status_text)

    @app_commands.command(name="profile", description="查看或編輯用戶檔案")
    @app_commands.checks.cooldown(1, 2.0, key=lambda i: i.user.id)
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        user="要查看檔案的用戶 (用戶名稱、顯示名稱或ID)",
        edit="是否開啟檔案設定介面",
        background_image="要設定為背景的圖片",
    )
    async def profile_command(
        self,
        interaction: discord.Interaction,
        user: Optional[Union[discord.Member, discord.User]] = None,
        edit: bool = False,
        background_image: Optional[discord.Attachment] = None,
    ):
        """顯示或編輯用戶個人檔案的指令"""
        target_member = None

        # 確定目標用戶
        if user is None:
            # 如果沒有提供用戶，目標就是指令發起者
            target_member = interaction.user
        else:
            # discord.py's app command transformer handles the conversion
            target_member = user

        if not target_member:
            raise BusinessError("無法確定目標用戶。")

        # 處理背景圖片上傳
        if background_image:
            if target_member.id != interaction.user.id:
                raise ProfileUpdateError("您只能設定自己的檔案。")

            await interaction.response.defer(ephemeral=True)

            target_user_id_for_bg = interaction.user.id

            await self._handle_set_background_image_to_local_storage(
                target_user_id_for_bg, background_image, interaction
            )
            embed = SuccessEmbed(
                description="已成功設定新的背景圖片。\n新的背景圖將在下次個人檔案刷新或重新生成時顯示。"
            )
            await interaction.followup.send(
                embed=embed,
                ephemeral=True,
            )
            return

        # 如果是編輯模式
        if edit:
            if target_member.id != interaction.user.id:
                raise ProfileUpdateError("您只能設定自己的檔案。")

            await interaction.response.defer(ephemeral=True)

            settings_view = ProfileSettingsView(self.bot, interaction.user.id)

            settings_embed = await settings_view.create_settings_embed()
            await interaction.followup.send(
                embed=settings_embed, view=settings_view, ephemeral=True
            )
            if hasattr(settings_view, "message"):
                settings_view.message = await interaction.original_response()
            return

        # 常規檔案顯示模式
        await interaction.response.defer(ephemeral=False, thinking=True)

        target_user_for_view = target_member
        target_user_id_for_view = target_user_for_view.id

        logger.info(
            "User %s is viewing profile of %s",
            interaction.user.id,
            target_user_id_for_view,
        )

        # 現在圖片生成和上傳邏輯已移至 ProfileService
        image_url = await profile_service.get_profile_image_url(
            bot=self.bot,
            user_id=target_user_id_for_view,
            user_name=target_user_for_view.display_name,
        )

        view = ProfileLikeView(
            owner_id=target_user_id_for_view,
            bot_instance=self.bot,
            private_channel_id=self.private_channel_id,
        )

        if image_url:
            await interaction.followup.send(image_url, view=view)
        else:
            # 如果生成失敗，發送一條錯誤訊息
            raise ProfileUpdateError("無法生成或獲取您的個人檔案圖片，請稍後再試。")

    @commands.Cog.listener()
    async def on_interaction(self, interaction: discord.Interaction):
        if interaction.type != discord.InteractionType.component:
            return

        custom_id = interaction.data.get("custom_id") if interaction.data else None
        if not custom_id:
            return

        if custom_id == "profile_like_button":
            pass


async def setup(bot: CustomAutoShardedBot):  # <-- 修正類型提示
    # ProfileCog 現在只接收 bot 作為參數
    try:
        await bot.add_cog(ProfileCog(bot))
        logger.info("ProfileCog added to bot.")
    except Exception as e:
        logger.error("Failed to add ProfileCog to bot: %s", e, exc_info=True)


# Ensure PlaywrightManager is imported if not already at the top
